# MICADA Division Monitoring System

A comprehensive digital dashboard for the MICADA Division to track new project proposals and monitor the progress of approved projects.

## 🚀 Quick Start

### Prerequisites
- Node.js (version 14 or higher)
- npm (comes with Node.js)

### Installation & Running

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Start the Server**
   ```bash
   npm start
   ```

3. **Access the Application**
   Open your browser and go to: `http://localhost:3000`

## 👥 User Accounts

### Executive Engineer (Xen) Account
- **Username:** `xenmicadahisar`
- **Password:** `dharmji@1947`
- **Role:** Full access to all subdivisions and user management

### Subdivision User Accounts
1. **MICAD Sub Division, Narwana**
   - Username: `narwana_user`
   - Password: `narwana123`

2. **Barwala CAD Subdivision**
   - Username: `barwala_user`
   - Password: `barwala123`

3. **CAD Subdivision 1 Hisar**
   - Username: `hisar1_user`
   - Password: `hisar1123`

4. **CAD Subdivision 2 Hisar**
   - Username: `hisar2_user`
   - Password: `hisar2123`

## 📋 Features

### Module 1: New Water Course Applications
- Track new applications from receipt to completion
- Status field serves as Action Taken Report (ATR)
- Add, edit, delete applications
- Bulk upload via Excel files
- Download Excel templates

### Module 2: Work Status Monitoring
- Monitor progress of approved construction works
- Track physical progress and contractor details
- Update length lined and remarks
- Bulk upload via Excel files
- Download Excel templates

### Xen Dashboard Features
- **Universal Search:** Search across all fields in both modules
- **Consolidated View:** See data from all subdivisions
- **User Management:** Update subdivision user credentials
- **Real-time Counts:** Dashboard shows current statistics

### Subdivision User Features
- **Data Entry:** Add and edit records for their subdivision
- **Status Updates:** Update application status and work progress
- **File Operations:** Download templates and bulk upload data
- **Secure Access:** Only see their own subdivision's data

## 🔍 How to Use

### For Executive Engineer (Xen)
1. Login with xen credentials
2. Use the universal search to find any information instantly
3. View consolidated data from all subdivisions
4. Manage user credentials in "Manage Users" section
5. Monitor overall division performance on dashboard

### For Subdivision Users
1. Login with subdivision credentials
2. Add new applications in "New Applications" section
3. Update work progress in "Work Status" section
4. Use bulk upload for multiple records
5. Download templates for proper data format

## 📊 Data Management

### Excel Templates
- Download templates from each module
- Fill with proper data format
- Upload for bulk data entry
- System validates and imports data

### Data Fields

**New Applications:**
- Sr. No, Village, Constituency, District
- RD, Minor, Status (ATR), Work Type
- Farmer's Name, Contact, Reference

**Work Status:**
- Village, Constituency, District, Water Course
- Work Name, AA No. & Date, Type of Work
- Total Length, Length Lined, Contractor, Remarks

## 🔒 Security Features
- JWT-based authentication
- Role-based access control
- Password encryption
- Session management
- Data isolation by subdivision

## 🛠️ Technical Details
- **Backend:** Node.js with Express
- **Frontend:** HTML5, CSS3, JavaScript, Bootstrap 5
- **Data Storage:** JSON files
- **File Upload:** Excel/CSV support
- **Authentication:** JWT tokens

## 📁 Project Structure
```
MICADA Website for division/
├── server.js              # Main server file
├── package.json           # Dependencies
├── public/                # Frontend files
│   ├── index.html        # Main HTML
│   ├── app.js            # JavaScript logic
│   └── styles.css        # Styling
├── data/                 # Data storage
│   ├── users.json        # User credentials
│   └── *_*.json          # Subdivision data files
└── uploads/              # Temporary upload folder
```

## 🔧 Maintenance

### Adding New Users
1. Login as Xen
2. Go to "Manage Users"
3. Click "Edit Credentials" for any subdivision
4. Update username and password

### Backup Data
- Copy the entire `data/` folder regularly
- JSON files contain all application and work data

### Server Management
- Server runs on port 3000 by default
- Logs are displayed in console
- Restart server if needed: `npm start`

## 📞 Support
For technical support or questions about the MICADA Division Monitoring System, contact the system administrator.

---

**Developed for MICADA Division, Hisar**  
*Digital transformation for efficient water course management*
