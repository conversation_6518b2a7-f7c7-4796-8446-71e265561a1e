// MICADA Division Monitoring System - Main Application JavaScript

// Global variables
let currentUser = null;
let authToken = null;
let currentSection = 'dashboard';

// Simple working functions (from app-clean.js)
function showAddApplicationModal() {
    console.log('showAddApplicationModal called');
    try {
        const modalElement = document.getElementById('applicationModal');
        if (!modalElement) {
            alert('Modal not found. Please refresh the page.');
            return;
        }

        document.getElementById('applicationModalTitle').textContent = 'Add New Application';
        document.getElementById('applicationForm').reset();
        document.getElementById('applicationId').value = '';

        const modal = new bootstrap.Modal(modalElement);
        modal.show();
        console.log('Modal shown successfully');
    } catch (error) {
        console.error('Error showing modal:', error);
        alert('Error opening form: ' + error.message);
    }
}

function showAddWorkModal() {
    console.log('showAddWorkModal called');
    try {
        document.getElementById('workModalTitle').textContent = 'Add New Work';
        document.getElementById('workForm').reset();
        document.getElementById('workId').value = '';

        const modal = new bootstrap.Modal(document.getElementById('workModal'));
        modal.show();
        console.log('Work modal shown');
    } catch (error) {
        console.error('Error showing work modal:', error);
        alert('Error opening form: ' + error.message);
    }
}

function downloadTemplate(type) {
    console.log('downloadTemplate called with type:', type);
    let headers, filename;

    if (type === 'applications') {
        headers = ['Sr. No', 'Village', 'Constituency', 'District', 'RD', 'MINOR', 'Status', 'Work Type', "Farmer's Name", 'Contact', 'Reference'];
        filename = 'new_applications_template.csv';
    } else if (type === 'works') {
        headers = ['Village', 'Constituency', 'District', 'Water Course Name', 'Name of Work', 'AA No. & Date', 'Type of Work', 'Total Length to be Lined', 'Length Lined', 'Contractor', 'Remarks'];
        filename = 'work_status_template.csv';
    }

    const csvContent = headers.join(',') + '\n';
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

function showUploadModal(type) {
    console.log('showUploadModal called with type:', type);
    document.getElementById('uploadType').value = type;
    document.getElementById('uploadModalTitle').textContent =
        type === 'applications' ? 'Bulk Upload Applications' : 'Bulk Upload Work Status';
    document.getElementById('uploadFile').value = '';

    const modal = new bootstrap.Modal(document.getElementById('uploadModal'));
    modal.show();
}

// Make functions globally accessible immediately
window.showAddApplicationModal = showAddApplicationModal;
window.showAddWorkModal = showAddWorkModal;
window.downloadTemplate = downloadTemplate;
window.showUploadModal = showUploadModal;
window.logout = logout;
window.showSection = showSection;

console.log('Basic functions defined and made global');

// Initialize application
document.addEventListener('DOMContentLoaded', function () {
    console.log('DOM loaded, initializing app...');
    checkAuthStatus();
    setupEventListeners();
});

// Check if user is already authenticated
function checkAuthStatus() {
    const token = localStorage.getItem('authToken');
    const user = localStorage.getItem('currentUser');

    if (token && user) {
        authToken = token;
        currentUser = JSON.parse(user);
        showMainApp();
    } else {
        showLoginModal();
    }
}

// Setup event listeners
function setupEventListeners() {
    // Login form
    document.getElementById('loginForm').addEventListener('submit', handleLogin);

    // Universal search for Xen
    const searchInput = document.getElementById('universalSearch');
    if (searchInput) {
        searchInput.addEventListener('input', handleUniversalSearch);
    }

    const searchInput2 = document.getElementById('universalSearch2');
    if (searchInput2) {
        searchInput2.addEventListener('input', handleUniversalSearchWorks);
    }

    // Event delegation for dynamic buttons
    document.addEventListener('click', function (e) {
        // Handle button clicks with data attributes or specific classes
        if (e.target.closest('button[onclick*="showAddApplicationModal"]')) {
            e.preventDefault();
            showAddApplicationModal();
        } else if (e.target.closest('button[onclick*="showAddWorkModal"]')) {
            e.preventDefault();
            showAddWorkModal();
        } else if (e.target.closest('button[onclick*="downloadTemplate"]')) {
            e.preventDefault();
            const type = e.target.closest('button').onclick.toString().includes('applications') ? 'applications' : 'works';
            downloadTemplate(type);
        } else if (e.target.closest('button[onclick*="showUploadModal"]')) {
            e.preventDefault();
            const type = e.target.closest('button').onclick.toString().includes('applications') ? 'applications' : 'works';
            showUploadModal(type);
        }
    });
}



// Handle login
async function handleLogin(e) {
    e.preventDefault();

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const errorDiv = document.getElementById('loginError');

    try {
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });

        const data = await response.json();

        if (response.ok) {
            authToken = data.token;
            currentUser = data.user;

            localStorage.setItem('authToken', authToken);
            localStorage.setItem('currentUser', JSON.stringify(currentUser));

            hideLoginModal();
            showMainApp();
        } else {
            errorDiv.textContent = data.error;
            errorDiv.classList.remove('d-none');
        }
    } catch (error) {
        console.error('Login error:', error);
        errorDiv.textContent = 'Connection error. Please try again.';
        errorDiv.classList.remove('d-none');
    }
}

// Show login modal
function showLoginModal() {
    const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
    loginModal.show();
}

// Hide login modal
function hideLoginModal() {
    const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
    if (loginModal) {
        loginModal.hide();
    }
}

// Show main application
function showMainApp() {
    document.getElementById('mainApp').classList.remove('d-none');
    document.getElementById('userInfo').textContent = `Welcome, ${currentUser.name}`;

    setupNavigation();
    loadDashboard();
}

// Setup navigation based on user role
function setupNavigation() {
    const sidebarNav = document.getElementById('sidebarNav');
    let navItems = [];

    // Common navigation items
    navItems.push({
        id: 'dashboard',
        icon: 'fas fa-tachometer-alt',
        text: 'Dashboard',
        onclick: 'showSection("dashboard")'
    });

    navItems.push({
        id: 'newApplications',
        icon: 'fas fa-file-alt',
        text: 'New Applications',
        onclick: 'showSection("newApplications")'
    });

    navItems.push({
        id: 'workStatus',
        icon: 'fas fa-tools',
        text: 'Work Status',
        onclick: 'showSection("workStatus")'
    });

    // Xen-only navigation
    if (currentUser.role === 'xen') {
        navItems.push({
            id: 'userManagement',
            icon: 'fas fa-users-cog',
            text: 'Manage Users',
            onclick: 'showSection("userManagement")'
        });
    }

    // Build navigation HTML
    sidebarNav.innerHTML = navItems.map(item => `
        <li class="nav-item">
            <a class="nav-link" href="#" id="nav-${item.id}" onclick="${item.onclick}">
                <i class="${item.icon}"></i>
                ${item.text}
            </a>
        </li>
    `).join('');

    // Set dashboard as active
    document.getElementById('nav-dashboard').classList.add('active');
}

// Show specific section
function showSection(section) {
    // Hide all content sections
    const sections = ['dashboardContent', 'newApplicationsContent', 'workStatusContent', 'userManagementContent'];
    sections.forEach(s => {
        document.getElementById(s).classList.add('d-none');
    });

    // Remove active class from all nav items
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });

    // Show selected section and set nav active
    currentSection = section;
    document.getElementById('nav-' + section).classList.add('active');

    switch (section) {
        case 'dashboard':
            document.getElementById('dashboardContent').classList.remove('d-none');
            document.getElementById('pageTitle').textContent = 'Dashboard';
            loadDashboard();
            break;
        case 'newApplications':
            document.getElementById('newApplicationsContent').classList.remove('d-none');
            document.getElementById('pageTitle').textContent = 'New Water Course Applications';
            loadNewApplications();
            break;
        case 'workStatus':
            document.getElementById('workStatusContent').classList.remove('d-none');
            document.getElementById('pageTitle').textContent = 'Work Status Monitoring';
            loadWorkStatus();
            break;
        case 'userManagement':
            if (currentUser.role === 'xen') {
                document.getElementById('userManagementContent').classList.remove('d-none');
                document.getElementById('pageTitle').textContent = 'User Management';
                loadUserManagement();
            }
            break;
    }
}

// Load dashboard data
async function loadDashboard() {
    try {
        // Load counts for dashboard cards
        const [newAppsResponse, workStatusResponse] = await Promise.all([
            fetch('/api/new-applications', {
                headers: { 'Authorization': `Bearer ${authToken}` }
            }),
            fetch('/api/work-status', {
                headers: { 'Authorization': `Bearer ${authToken}` }
            })
        ]);

        const newApps = await newAppsResponse.json();
        const workStatus = await workStatusResponse.json();

        document.getElementById('newAppsCount').textContent = newApps.length;
        document.getElementById('activeWorksCount').textContent = workStatus.length;

    } catch (error) {
        console.error('Error loading dashboard:', error);
    }
}

// Load new applications
async function loadNewApplications() {
    console.log('Loading new applications...');
    try {
        console.log('Making API call to /api/new-applications');
        console.log('Auth token:', authToken ? 'Present' : 'Missing');
        console.log('Current user:', currentUser);

        const response = await fetch('/api/new-applications', {
            headers: { 'Authorization': `Bearer ${authToken}` }
        });

        console.log('API response status:', response.status);

        if (!response.ok) {
            console.error('API response not OK:', response.status, response.statusText);
            const errorText = await response.text();
            console.error('Error response:', errorText);
            alert('Failed to load applications. Check console for details.');
            return;
        }

        const applications = await response.json();
        console.log('Applications loaded:', applications.length, 'items');
        console.log('Applications data:', applications);

        displayApplicationsTable(applications);

        // Show search for Xen users
        if (currentUser.role === 'xen') {
            document.getElementById('xenSearchContainer').classList.remove('d-none');
        }

        // Setup button event listeners
        setupApplicationButtons();

        console.log('New applications section loaded successfully');

    } catch (error) {
        console.error('Error loading applications:', error);
        alert('Error loading applications: ' + error.message);
    }
}

// Setup button event listeners for applications section
function setupApplicationButtons() {
    console.log('Setting up application buttons...');

    // Check if we're using onclick or need event listeners
    const buttonsWithOnclick = document.querySelectorAll('button[onclick*="showAddApplicationModal"]');
    console.log('Buttons with onclick found:', buttonsWithOnclick.length);

    if (buttonsWithOnclick.length > 0) {
        console.log('Using onclick handlers - no additional setup needed');
        return;
    }

    // Fallback: Add event listeners to ID-based buttons
    const addBtn = document.getElementById('addApplicationBtn');
    const downloadBtn = document.getElementById('downloadApplicationTemplateBtn');
    const uploadBtn = document.getElementById('uploadApplicationBtn');

    console.log('Button elements found:');
    console.log('- Add button:', addBtn);
    console.log('- Download button:', downloadBtn);
    console.log('- Upload button:', uploadBtn);

    if (addBtn) {
        addBtn.addEventListener('click', function (e) {
            e.preventDefault();
            console.log('Add Application button clicked via event listener');
            showAddApplicationModal();
        });
        console.log('Add button event listener attached');
    }

    if (downloadBtn) {
        downloadBtn.addEventListener('click', function (e) {
            e.preventDefault();
            console.log('Download Template button clicked via event listener');
            downloadTemplate('applications');
        });
        console.log('Download button event listener attached');
    }

    if (uploadBtn) {
        uploadBtn.addEventListener('click', function (e) {
            e.preventDefault();
            console.log('Upload button clicked via event listener');
            showUploadModal('applications');
        });
        console.log('Upload button event listener attached');
    }

    console.log('Application buttons setup complete');
}

// Display applications table
function displayApplicationsTable(applications) {
    const headerRow = document.getElementById('applicationsTableHeader');
    const tbody = document.getElementById('applicationsTableBody');

    // Define headers
    let headers = ['Sr. No', 'Village', 'Constituency', 'District', 'RD', 'Minor', 'Status', 'Work Type', 'Farmer Name', 'Contact', 'Reference'];

    if (currentUser.role === 'xen') {
        headers.push('Subdivision');
    }

    if (currentUser.role === 'subdivision') {
        headers.push('Actions');
    }

    // Build header
    headerRow.innerHTML = headers.map(header => `<th>${header}</th>`).join('');

    // Build table body
    tbody.innerHTML = applications.map(app => {
        let row = `
            <tr>
                <td>${app.sr_no || ''}</td>
                <td>${app.village || ''}</td>
                <td>${app.constituency || ''}</td>
                <td>${app.district || ''}</td>
                <td>${app.rd || ''}</td>
                <td>${app.minor || ''}</td>
                <td><span class="badge status-${getStatusClass(app.status)}">${app.status || ''}</span></td>
                <td>${app.work_type || ''}</td>
                <td>${app.farmer_name || ''}</td>
                <td>${app.contact || ''}</td>
                <td>${app.reference || ''}</td>
        `;

        if (currentUser.role === 'xen') {
            row += `<td>${app.subdivision || ''}</td>`;
        }

        if (currentUser.role === 'subdivision') {
            row += `
                <td>
                    <button class="btn btn-sm btn-primary me-1" onclick="editApplication('${app.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteApplication('${app.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
        }

        row += '</tr>';
        return row;
    }).join('');
}

// Get status CSS class
function getStatusClass(status) {
    if (!status) return 'pending';
    const statusLower = status.toLowerCase();
    if (statusLower.includes('approved')) return 'approved';
    if (statusLower.includes('rejected')) return 'rejected';
    if (statusLower.includes('progress')) return 'in-progress';
    return 'pending';
}

// Logout function
function logout() {
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    location.reload();
}

// Utility function for API calls
async function apiCall(endpoint, options = {}) {
    const defaultOptions = {
        headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
        }
    };

    const finalOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };

    const response = await fetch(endpoint, finalOptions);

    if (response.status === 401) {
        logout();
        return;
    }

    return response;
}

// Load work status
async function loadWorkStatus() {
    try {
        const response = await fetch('/api/work-status', {
            headers: { 'Authorization': `Bearer ${authToken}` }
        });

        const works = await response.json();
        displayWorkStatusTable(works);

        // Show search for Xen users
        if (currentUser.role === 'xen') {
            document.getElementById('xenSearchContainer2').classList.remove('d-none');
        }

        // Setup button event listeners
        setupWorkStatusButtons();

    } catch (error) {
        console.error('Error loading work status:', error);
    }
}

// Setup button event listeners for work status section
function setupWorkStatusButtons() {
    // Add Work button
    const addBtn = document.getElementById('addWorkBtn');
    if (addBtn) {
        addBtn.addEventListener('click', function (e) {
            e.preventDefault();
            console.log('Add Work button clicked');
            showAddWorkModal();
        });
    }

    // Download Template button
    const downloadBtn = document.getElementById('downloadWorkTemplateBtn');
    if (downloadBtn) {
        downloadBtn.addEventListener('click', function (e) {
            e.preventDefault();
            console.log('Download Work Template button clicked');
            downloadTemplate('works');
        });
    }

    // Upload button
    const uploadBtn = document.getElementById('uploadWorkBtn');
    if (uploadBtn) {
        uploadBtn.addEventListener('click', function (e) {
            e.preventDefault();
            console.log('Upload Work button clicked');
            showUploadModal('works');
        });
    }
}

// Display work status table
function displayWorkStatusTable(works) {
    const headerRow = document.getElementById('workStatusTableHeader');
    const tbody = document.getElementById('workStatusTableBody');

    // Define headers
    let headers = ['Village', 'Constituency', 'District', 'Water Course', 'Work Name', 'AA No. & Date', 'Type of Work', 'Total Length', 'Length Lined', 'Contractor', 'Remarks'];

    if (currentUser.role === 'xen') {
        headers.push('Subdivision');
    }

    if (currentUser.role === 'subdivision') {
        headers.push('Actions');
    }

    // Build header
    headerRow.innerHTML = headers.map(header => `<th>${header}</th>`).join('');

    // Build table body
    tbody.innerHTML = works.map(work => {
        let row = `
            <tr>
                <td>${work.village || ''}</td>
                <td>${work.constituency || ''}</td>
                <td>${work.district || ''}</td>
                <td>${work.water_course || ''}</td>
                <td>${work.work_name || ''}</td>
                <td>${work.aa_no_date || ''}</td>
                <td>${work.type_of_work || ''}</td>
                <td>${work.total_length_to_be_lined || ''}</td>
                <td>${work.length_lined || ''}</td>
                <td>${work.contractor || ''}</td>
                <td>${work.remarks || ''}</td>
        `;

        if (currentUser.role === 'xen') {
            row += `<td>${work.subdivision || ''}</td>`;
        }

        if (currentUser.role === 'subdivision') {
            row += `
                <td>
                    <button class="btn btn-sm btn-primary me-1" onclick="editWork('${work.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteWork('${work.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
        }

        row += '</tr>';
        return row;
    }).join('');
}

// Load user management (Xen only)
async function loadUserManagement() {
    if (currentUser.role !== 'xen') return;

    try {
        const response = await fetch('/api/users', {
            headers: { 'Authorization': `Bearer ${authToken}` }
        });

        const users = await response.json();
        displayUsersTable(users);

    } catch (error) {
        console.error('Error loading users:', error);
    }
}

// Display users table
function displayUsersTable(users) {
    const tbody = document.getElementById('usersTableBody');

    tbody.innerHTML = users.map(user => `
        <tr>
            <td>${user.subdivision}</td>
            <td>${user.username}</td>
            <td>
                <button class="btn btn-sm btn-primary" onclick="editUser('${user.id}', '${user.username}', '${user.subdivision}')">
                    <i class="fas fa-edit me-1"></i>Edit Credentials
                </button>
            </td>
        </tr>
    `).join('');
}

// Universal search functionality for applications
function handleUniversalSearch(e) {
    const searchTerm = e.target.value.toLowerCase();
    const tableRows = document.querySelectorAll('#applicationsTableBody tr');

    tableRows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
            // Highlight matching text
            highlightSearchTerm(row, searchTerm);
        } else {
            row.style.display = 'none';
        }
    });
}

// Universal search functionality for work status
function handleUniversalSearchWorks(e) {
    const searchTerm = e.target.value.toLowerCase();
    const tableRows = document.querySelectorAll('#workStatusTableBody tr');

    tableRows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
            highlightSearchTerm(row, searchTerm);
        } else {
            row.style.display = 'none';
        }
    });
}

// Highlight search terms
function highlightSearchTerm(row, searchTerm) {
    if (!searchTerm) return;

    const cells = row.querySelectorAll('td');
    cells.forEach(cell => {
        const originalText = cell.textContent;
        const regex = new RegExp(`(${searchTerm})`, 'gi');
        const highlightedText = originalText.replace(regex, '<span class="search-highlight">$1</span>');

        // Only update if there's a match and no buttons in the cell
        if (highlightedText !== originalText && !cell.querySelector('button')) {
            cell.innerHTML = highlightedText;
        }
    });
}



// Edit application
async function editApplication(id) {
    try {
        const response = await fetch('/api/new-applications', {
            headers: { 'Authorization': `Bearer ${authToken}` }
        });

        const applications = await response.json();
        const application = applications.find(app => app.id === id);

        if (application) {
            document.getElementById('applicationModalTitle').textContent = 'Edit Application';
            document.getElementById('applicationId').value = application.id;
            document.getElementById('sr_no').value = application.sr_no || '';
            document.getElementById('village').value = application.village || '';
            document.getElementById('constituency').value = application.constituency || '';
            document.getElementById('district').value = application.district || '';
            document.getElementById('rd').value = application.rd || '';
            document.getElementById('minor').value = application.minor || '';
            document.getElementById('status').value = application.status || '';
            document.getElementById('work_type').value = application.work_type || '';
            document.getElementById('farmer_name').value = application.farmer_name || '';
            document.getElementById('contact').value = application.contact || '';
            document.getElementById('reference').value = application.reference || '';

            const modal = new bootstrap.Modal(document.getElementById('applicationModal'));
            modal.show();
        }
    } catch (error) {
        console.error('Error loading application for edit:', error);
        alert('Error loading application data');
    }
}

// Edit work
async function editWork(id) {
    try {
        const response = await fetch('/api/work-status', {
            headers: { 'Authorization': `Bearer ${authToken}` }
        });

        const works = await response.json();
        const work = works.find(w => w.id === id);

        if (work) {
            document.getElementById('workModalTitle').textContent = 'Edit Work';
            document.getElementById('workId').value = work.id;
            document.getElementById('work_village').value = work.village || '';
            document.getElementById('work_constituency').value = work.constituency || '';
            document.getElementById('work_district').value = work.district || '';
            document.getElementById('water_course').value = work.water_course || '';
            document.getElementById('work_name').value = work.work_name || '';
            document.getElementById('aa_no_date').value = work.aa_no_date || '';
            document.getElementById('type_of_work').value = work.type_of_work || '';
            document.getElementById('total_length_to_be_lined').value = work.total_length_to_be_lined || '';
            document.getElementById('length_lined').value = work.length_lined || '';
            document.getElementById('contractor').value = work.contractor || '';
            document.getElementById('remarks').value = work.remarks || '';

            const modal = new bootstrap.Modal(document.getElementById('workModal'));
            modal.show();
        }
    } catch (error) {
        console.error('Error loading work for edit:', error);
        alert('Error loading work data');
    }
}

// Edit user (Xen only)
function editUser(userId, username, subdivision) {
    document.getElementById('editUserId').value = userId;
    document.getElementById('editSubdivision').value = subdivision;
    document.getElementById('editUsername').value = username;
    document.getElementById('editPassword').value = '';
    document.getElementById('confirmPassword').value = '';

    const modal = new bootstrap.Modal(document.getElementById('userModal'));
    modal.show();
}

// Save application
async function saveApplication() {
    const applicationId = document.getElementById('applicationId').value;
    const isEdit = !!applicationId;

    const applicationData = {
        sr_no: document.getElementById('sr_no').value,
        village: document.getElementById('village').value,
        constituency: document.getElementById('constituency').value,
        district: document.getElementById('district').value,
        rd: document.getElementById('rd').value,
        minor: document.getElementById('minor').value,
        status: document.getElementById('status').value,
        work_type: document.getElementById('work_type').value,
        farmer_name: document.getElementById('farmer_name').value,
        contact: document.getElementById('contact').value,
        reference: document.getElementById('reference').value
    };

    try {
        const url = isEdit ? `/api/new-applications/${applicationId}` : '/api/new-applications';
        const method = isEdit ? 'PUT' : 'POST';

        const response = await apiCall(url, {
            method: method,
            body: JSON.stringify(applicationData)
        });

        if (response.ok) {
            const modal = bootstrap.Modal.getInstance(document.getElementById('applicationModal'));
            modal.hide();

            loadNewApplications(); // Reload the table
            loadDashboard(); // Update dashboard counts

            alert(isEdit ? 'Application updated successfully' : 'Application added successfully');
        } else {
            const error = await response.json();
            alert('Error: ' + error.error);
        }
    } catch (error) {
        console.error('Error saving application:', error);
        alert('Error saving application');
    }
}

// Save work
async function saveWork() {
    const workId = document.getElementById('workId').value;
    const isEdit = !!workId;

    const workData = {
        village: document.getElementById('work_village').value,
        constituency: document.getElementById('work_constituency').value,
        district: document.getElementById('work_district').value,
        water_course: document.getElementById('water_course').value,
        work_name: document.getElementById('work_name').value,
        aa_no_date: document.getElementById('aa_no_date').value,
        type_of_work: document.getElementById('type_of_work').value,
        total_length_to_be_lined: document.getElementById('total_length_to_be_lined').value,
        length_lined: document.getElementById('length_lined').value,
        contractor: document.getElementById('contractor').value,
        remarks: document.getElementById('remarks').value
    };

    try {
        const url = isEdit ? `/api/work-status/${workId}` : '/api/work-status';
        const method = isEdit ? 'PUT' : 'POST';

        const response = await apiCall(url, {
            method: method,
            body: JSON.stringify(workData)
        });

        if (response.ok) {
            const modal = bootstrap.Modal.getInstance(document.getElementById('workModal'));
            modal.hide();

            loadWorkStatus(); // Reload the table
            loadDashboard(); // Update dashboard counts

            alert(isEdit ? 'Work updated successfully' : 'Work added successfully');
        } else {
            const error = await response.json();
            alert('Error: ' + error.error);
        }
    } catch (error) {
        console.error('Error saving work:', error);
        alert('Error saving work');
    }
}

// Delete application
async function deleteApplication(id) {
    if (!confirm('Are you sure you want to delete this application?')) return;

    try {
        const response = await apiCall(`/api/new-applications/${id}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            loadNewApplications(); // Reload the table
            alert('Application deleted successfully');
        } else {
            const error = await response.json();
            alert('Error: ' + error.error);
        }
    } catch (error) {
        console.error('Error deleting application:', error);
        alert('Error deleting application');
    }
}

// Delete work
async function deleteWork(id) {
    if (!confirm('Are you sure you want to delete this work?')) return;

    try {
        const response = await apiCall(`/api/work-status/${id}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            loadWorkStatus(); // Reload the table
            alert('Work deleted successfully');
        } else {
            const error = await response.json();
            alert('Error: ' + error.error);
        }
    } catch (error) {
        console.error('Error deleting work:', error);
        alert('Error deleting work');
    }
}

// Save user (Xen only)
async function saveUser() {
    const userId = document.getElementById('editUserId').value;
    const username = document.getElementById('editUsername').value;
    const password = document.getElementById('editPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    if (password !== confirmPassword) {
        alert('Passwords do not match');
        return;
    }

    try {
        const response = await apiCall(`/api/users/${userId}`, {
            method: 'PUT',
            body: JSON.stringify({ username, password })
        });

        if (response.ok) {
            const modal = bootstrap.Modal.getInstance(document.getElementById('userModal'));
            modal.hide();

            loadUserManagement(); // Reload the table
            alert('User credentials updated successfully');
        } else {
            const error = await response.json();
            alert('Error: ' + error.error);
        }
    } catch (error) {
        console.error('Error updating user:', error);
        alert('Error updating user credentials');
    }
}

// Upload file
async function uploadFile() {
    const fileInput = document.getElementById('uploadFile');
    const uploadType = document.getElementById('uploadType').value;

    if (!fileInput.files[0]) {
        alert('Please select a file');
        return;
    }

    const formData = new FormData();
    formData.append('file', fileInput.files[0]);

    try {
        const endpoint = uploadType === 'applications' ?
            '/api/upload/new-applications' : '/api/upload/work-status';

        const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`
            },
            body: formData
        });

        if (response.ok) {
            const result = await response.json();

            const modal = bootstrap.Modal.getInstance(document.getElementById('uploadModal'));
            modal.hide();

            // Reload appropriate section
            if (uploadType === 'applications') {
                loadNewApplications();
            } else {
                loadWorkStatus();
            }

            loadDashboard(); // Update dashboard counts
            alert(result.message);
        } else {
            const error = await response.json();
            alert('Upload error: ' + error.error);
        }
    } catch (error) {
        console.error('Error uploading file:', error);
        alert('Error uploading file');
    }
}
