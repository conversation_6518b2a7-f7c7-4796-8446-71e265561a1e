// Firebase Setup Script - Run this once to initialize your database
import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
import { getFirestore, collection, addDoc, doc, setDoc } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

// Your Firebase configuration - Updated with correct project details
const firebaseConfig = {
    apiKey: "AIzaSyC4bwhHbZ9oc6mPkzIbQKJyIk9sSiA-S9s",
    authDomain: "micada-division.firebaseapp.com",
    projectId: "micada-division",
    storageBucket: "micada-division.appspot.com",
    messagingSenderId: "363841115848",
    appId: "1:363841115848:web:68ce295c4375e68fe077fd",
    measurementId: "G-1RXZT0K512"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Setup function
async function setupFirebaseData() {
    console.log('🔥 Starting Firebase setup...');

    try {
        // 1. Create Subdivisions
        console.log('📁 Creating subdivisions...');
        await createSubdivisions();

        // 2. Create Authorized Users
        console.log('👥 Creating authorized users...');
        await createAuthorizedUsers();

        // 3. Create Sample Applications
        console.log('📄 Creating sample applications...');
        await createSampleApplications();

        // 4. Create Sample Work Status
        console.log('🔧 Creating sample work status...');
        await createSampleWorkStatus();

        // 5. Create System Settings
        console.log('⚙️ Creating system settings...');
        await createSystemSettings();

        console.log('✅ Firebase setup completed successfully!');
        alert('Firebase setup completed! You can now use the application.');

    } catch (error) {
        console.error('❌ Error during setup:', error);
        alert('Setup failed: ' + error.message);
    }
}

// Create subdivisions
async function createSubdivisions() {
    const subdivisions = [
        {
            id: 'narwana',
            name: 'MICAD Sub Division, Narwana',
            code: 'narwana',
            active: true,
            district: 'Jind',
            createdAt: new Date()
        },
        {
            id: 'barwala',
            name: 'Barwala CAD Subdivision',
            code: 'barwala',
            active: true,
            district: 'Hisar',
            createdAt: new Date()
        },
        {
            id: 'hisar1',
            name: 'CAD Subdivision 1 Hisar',
            code: 'hisar1',
            active: true,
            district: 'Hisar',
            createdAt: new Date()
        },
        {
            id: 'hisar2',
            name: 'CAD Subdivision 2 Hisar',
            code: 'hisar2',
            active: true,
            district: 'Hisar',
            createdAt: new Date()
        }
    ];

    for (const subdivision of subdivisions) {
        await setDoc(doc(db, 'subdivisions', subdivision.id), subdivision);
        console.log(`✓ Created subdivision: ${subdivision.name}`);
    }
}

// Create authorized users
async function createAuthorizedUsers() {
    const authorizedUsers = [
        {
            id: 'narwana_user',
            email: '<EMAIL>',
            role: 'subdivision',
            subdivision: 'narwana',
            name: 'MICAD Sub Division, Narwana',
            active: true,
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        },
        {
            id: 'barwala_user',
            email: '<EMAIL>',
            role: 'subdivision',
            subdivision: 'barwala',
            name: 'Barwala CAD Subdivision',
            active: true,
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        },
        {
            id: 'hisar1_user',
            email: '<EMAIL>',
            role: 'subdivision',
            subdivision: 'hisar1',
            name: 'CAD Subdivision 1 Hisar',
            active: true,
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        },
        {
            id: 'hisar2_user',
            email: '<EMAIL>',
            role: 'subdivision',
            subdivision: 'hisar2',
            name: 'CAD Subdivision 2 Hisar',
            active: true,
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        }
    ];

    for (const user of authorizedUsers) {
        await setDoc(doc(db, 'authorized_users', user.id), user);
        console.log(`✓ Created authorized user: ${user.email}`);
    }
}

// Create system settings
async function createSystemSettings() {
    const settings = {
        systemName: 'MICADA Division Monitoring System',
        version: '1.0.0',
        xenEmail: '<EMAIL>',
        lastUpdated: new Date(),
        features: {
            userManagement: true,
            bulkUpload: true,
            exportData: true,
            realTimeSync: true
        },
        subdivisions: ['narwana', 'barwala', 'hisar1', 'hisar2']
    };

    await setDoc(doc(db, 'system_settings', 'main'), settings);
    console.log('✓ Created system settings');
}

// Create sample applications
async function createSampleApplications() {
    const sampleApplications = [
        {
            sr_no: '001/2024',
            village: 'Narwana',
            constituency: 'Narwana',
            district: 'Jind',
            rd: 'RD-001',
            minor: 'Minor-A1',
            status: 'Pending Review',
            work_type: 'New Water Course Construction',
            farmer_name: 'Ram Singh Dahiya',
            contact: '9876543210',
            reference: 'MICADA/NAR/2024/001',
            subdivision: 'narwana',
            applicationDate: new Date('2024-01-15'),
            estimatedCost: '₹2,50,000',
            landArea: '5 Acres',
            cropType: 'Wheat, Rice',
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        },
        {
            sr_no: '002/2024',
            village: 'Barwala',
            constituency: 'Barwala',
            district: 'Hisar',
            rd: 'RD-002',
            minor: 'Minor-B2',
            status: 'Approved',
            work_type: 'Water Course Renovation',
            farmer_name: 'Suresh Kumar Jangra',
            contact: '9876543211',
            reference: 'MICADA/BAR/2024/002',
            subdivision: 'barwala',
            applicationDate: new Date('2024-01-20'),
            estimatedCost: '₹1,80,000',
            landArea: '3.5 Acres',
            cropType: 'Cotton, Mustard',
            approvalDate: new Date('2024-02-01'),
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        },
        {
            sr_no: '003/2024',
            village: 'Hisar',
            constituency: 'Hisar',
            district: 'Hisar',
            rd: 'RD-003',
            minor: 'Minor-C3',
            status: 'In Progress',
            work_type: 'Water Course Extension',
            farmer_name: 'Rajesh Sharma',
            contact: '9876543212',
            reference: 'MICADA/HIS1/2024/003',
            subdivision: 'hisar1',
            applicationDate: new Date('2024-01-25'),
            estimatedCost: '₹3,20,000',
            landArea: '7 Acres',
            cropType: 'Sugarcane, Fodder',
            workStartDate: new Date('2024-02-10'),
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        },
        {
            sr_no: '004/2024',
            village: 'Hansi',
            constituency: 'Hansi',
            district: 'Hisar',
            rd: 'RD-004',
            minor: 'Minor-D4',
            status: 'Under Review',
            work_type: 'New Outlet Construction',
            farmer_name: 'Mohan Lal Bishnoi',
            contact: '9876543213',
            reference: 'MICADA/HIS2/2024/004',
            subdivision: 'hisar2',
            applicationDate: new Date('2024-02-01'),
            estimatedCost: '₹1,50,000',
            landArea: '4 Acres',
            cropType: 'Bajra, Jowar',
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        },
        {
            sr_no: '005/2024',
            village: 'Jind',
            constituency: 'Jind',
            district: 'Jind',
            rd: 'RD-005',
            minor: 'Minor-E5',
            status: 'Rejected',
            work_type: 'Water Course Repair',
            farmer_name: 'Krishan Kumar Malik',
            contact: '9876543214',
            reference: 'MICADA/NAR/2024/005',
            subdivision: 'narwana',
            applicationDate: new Date('2024-02-05'),
            estimatedCost: '₹95,000',
            landArea: '2.5 Acres',
            cropType: 'Vegetables',
            rejectionReason: 'Insufficient water availability in the area',
            rejectionDate: new Date('2024-02-15'),
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        },
        {
            sr_no: '006/2024',
            village: 'Fatehabad',
            constituency: 'Fatehabad',
            district: 'Fatehabad',
            rd: 'RD-006',
            minor: 'Minor-F6',
            status: 'Completed',
            work_type: 'Water Course Lining',
            farmer_name: 'Baldev Singh',
            contact: '9876543215',
            reference: 'MICADA/BAR/2024/006',
            subdivision: 'barwala',
            applicationDate: new Date('2023-12-10'),
            estimatedCost: '₹2,80,000',
            landArea: '6 Acres',
            cropType: 'Rice, Wheat',
            workStartDate: new Date('2024-01-05'),
            completionDate: new Date('2024-02-20'),
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        },
        {
            sr_no: '007/2024',
            village: 'Tohana',
            constituency: 'Tohana',
            district: 'Fatehabad',
            rd: 'RD-007',
            minor: 'Minor-G7',
            status: 'Pending Approval',
            work_type: 'New Distribution System',
            farmer_name: 'Jagdish Chander',
            contact: '9876543216',
            reference: 'MICADA/HIS1/2024/007',
            subdivision: 'hisar1',
            applicationDate: new Date('2024-02-10'),
            estimatedCost: '₹4,50,000',
            landArea: '10 Acres',
            cropType: 'Cotton, Wheat',
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        }
    ];

    for (const application of sampleApplications) {
        await addDoc(collection(db, 'new_applications'), application);
        console.log(`✓ Created application: ${application.sr_no} - ${application.farmer_name}`);
    }
}

// Create sample work status
async function createSampleWorkStatus() {
    const sampleWorks = [
        {
            village: 'Narwana',
            constituency: 'Narwana',
            district: 'Jind',
            water_course: 'Narwana Main Canal - Section A',
            work_name: 'Concrete Lining of Water Course',
            aa_no_date: 'AA/MICADA/2024/001 dt. 15-01-2024',
            type_of_work: 'RCC Lining with Side Walls',
            total_length_to_be_lined: '2000 meters',
            length_lined: '1200 meters',
            contractor: 'Haryana Construction Company Ltd.',
            remarks: 'Work in progress, 60% completed. Expected completion by March 2024',
            subdivision: 'narwana',
            workStatus: 'In Progress',
            startDate: new Date('2024-01-20'),
            expectedCompletion: new Date('2024-03-15'),
            actualCost: '₹18,50,000',
            approvedCost: '₹20,00,000',
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        },
        {
            village: 'Barwala',
            constituency: 'Barwala',
            district: 'Hisar',
            water_course: 'Barwala Distribution System - Branch B',
            work_name: 'Construction of New RCC Outlet',
            aa_no_date: 'AA/MICADA/2024/002 dt. 20-01-2024',
            type_of_work: 'RCC Outlet with Control Gates',
            total_length_to_be_lined: '500 meters',
            length_lined: '500 meters',
            contractor: 'Punjab Engineering Works Pvt. Ltd.',
            remarks: 'Work completed successfully. Quality inspection passed.',
            subdivision: 'barwala',
            workStatus: 'Completed',
            startDate: new Date('2024-01-25'),
            completionDate: new Date('2024-02-18'),
            actualCost: '₹12,80,000',
            approvedCost: '₹13,00,000',
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        },
        {
            village: 'Hisar',
            constituency: 'Hisar',
            district: 'Hisar',
            water_course: 'Hisar Branch Canal - Section C',
            work_name: 'Repair and Maintenance of Existing Lining',
            aa_no_date: 'AA/MICADA/2024/003 dt. 25-01-2024',
            type_of_work: 'Brick Lining Repair and Plastering',
            total_length_to_be_lined: '1500 meters',
            length_lined: '800 meters',
            contractor: 'Rajasthan Infrastructure Builders',
            remarks: 'Work ongoing, delayed due to monsoon. 53% completed.',
            subdivision: 'hisar1',
            workStatus: 'Delayed',
            startDate: new Date('2024-02-01'),
            expectedCompletion: new Date('2024-04-30'),
            actualCost: '₹8,50,000',
            approvedCost: '₹15,00,000',
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        },
        {
            village: 'Hansi',
            constituency: 'Hansi',
            district: 'Hisar',
            water_course: 'Hansi Minor Canal - Section D',
            work_name: 'De-silting and Channel Cleaning',
            aa_no_date: 'AA/MICADA/2024/004 dt. 30-01-2024',
            type_of_work: 'Mechanical De-silting and Manual Cleaning',
            total_length_to_be_lined: '3000 meters',
            length_lined: '0 meters',
            contractor: 'Haryana Earth Movers Pvt. Ltd.',
            remarks: 'Work not yet started, pending environmental clearance',
            subdivision: 'hisar2',
            workStatus: 'Pending Clearance',
            expectedStartDate: new Date('2024-03-15'),
            expectedCompletion: new Date('2024-05-30'),
            approvedCost: '₹22,00,000',
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        },
        {
            village: 'Fatehabad',
            constituency: 'Fatehabad',
            district: 'Fatehabad',
            water_course: 'Fatehabad Main Canal - Section E',
            work_name: 'Construction of Check Dam',
            aa_no_date: 'AA/MICADA/2024/005 dt. 05-02-2024',
            type_of_work: 'RCC Check Dam with Gates',
            total_length_to_be_lined: '200 meters',
            length_lined: '150 meters',
            contractor: 'Delhi Construction Corporation',
            remarks: 'Work 75% completed. Final finishing in progress.',
            subdivision: 'barwala',
            workStatus: 'Near Completion',
            startDate: new Date('2024-02-10'),
            expectedCompletion: new Date('2024-03-10'),
            actualCost: '₹35,00,000',
            approvedCost: '₹38,00,000',
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        },
        {
            village: 'Jind',
            constituency: 'Jind',
            district: 'Jind',
            water_course: 'Jind Distribution Network - Phase 2',
            work_name: 'Installation of Flow Measurement System',
            aa_no_date: 'AA/MICADA/2024/006 dt. 12-02-2024',
            type_of_work: 'Digital Flow Meters and Control System',
            total_length_to_be_lined: '1000 meters',
            length_lined: '300 meters',
            contractor: 'Irrigation Technology Solutions Ltd.',
            remarks: 'Modern flow measurement system installation. 30% completed.',
            subdivision: 'narwana',
            workStatus: 'In Progress',
            startDate: new Date('2024-02-15'),
            expectedCompletion: new Date('2024-04-15'),
            actualCost: '₹8,00,000',
            approvedCost: '₹25,00,000',
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        }
    ];

    for (const work of sampleWorks) {
        await addDoc(collection(db, 'work_status'), work);
        console.log(`✓ Created work: ${work.work_name} - ${work.village}`);
    }
}

// Make setup function globally accessible
window.setupFirebaseData = setupFirebaseData;

console.log('🔥 Firebase setup script loaded. Call setupFirebaseData() to initialize.');
