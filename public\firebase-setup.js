// Firebase Setup Script - Run this once to initialize your database
import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
import { getFirestore, collection, addDoc, doc, setDoc } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

// Your Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyC4bwhHbZ9oc6mPkzIbQKJyIk9sSiA-S9s",
  authDomain: "micada-division.firebaseapp.com",
  projectId: "micada-division",
  storageBucket: "micada-division.firebasestorage.app",
  messagingSenderId: "363841115848",
  appId: "1:363841115848:web:68ce295c4375e68fe077fd",
  measurementId: "G-1RXZT0K512"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Setup function
async function setupFirebaseData() {
    console.log('🔥 Starting Firebase setup...');
    
    try {
        // 1. Create Subdivisions
        console.log('📁 Creating subdivisions...');
        await createSubdivisions();
        
        // 2. Create Sample Applications
        console.log('📄 Creating sample applications...');
        await createSampleApplications();
        
        // 3. Create Sample Work Status
        console.log('🔧 Creating sample work status...');
        await createSampleWorkStatus();
        
        console.log('✅ Firebase setup completed successfully!');
        alert('Firebase setup completed! You can now use the application.');
        
    } catch (error) {
        console.error('❌ Error during setup:', error);
        alert('Setup failed: ' + error.message);
    }
}

// Create subdivisions
async function createSubdivisions() {
    const subdivisions = [
        {
            id: 'narwana',
            name: 'MICAD Sub Division, Narwana',
            code: 'narwana',
            active: true,
            district: 'Jind',
            createdAt: new Date()
        },
        {
            id: 'barwala',
            name: 'Barwala CAD Subdivision',
            code: 'barwala',
            active: true,
            district: 'Hisar',
            createdAt: new Date()
        },
        {
            id: 'hisar1',
            name: 'CAD Subdivision 1 Hisar',
            code: 'hisar1',
            active: true,
            district: 'Hisar',
            createdAt: new Date()
        },
        {
            id: 'hisar2',
            name: 'CAD Subdivision 2 Hisar',
            code: 'hisar2',
            active: true,
            district: 'Hisar',
            createdAt: new Date()
        }
    ];
    
    for (const subdivision of subdivisions) {
        await setDoc(doc(db, 'subdivisions', subdivision.id), subdivision);
        console.log(`✓ Created subdivision: ${subdivision.name}`);
    }
}

// Create sample applications
async function createSampleApplications() {
    const sampleApplications = [
        {
            sr_no: '001',
            village: 'Narwana',
            constituency: 'Narwana',
            district: 'Jind',
            rd: 'RD-001',
            minor: 'Minor-A',
            status: 'Pending',
            work_type: 'New Water Course',
            farmer_name: 'Ram Singh',
            contact: '9876543210',
            reference: 'REF-001',
            subdivision: 'narwana',
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        },
        {
            sr_no: '002',
            village: 'Barwala',
            constituency: 'Barwala',
            district: 'Hisar',
            rd: 'RD-002',
            minor: 'Minor-B',
            status: 'Approved',
            work_type: 'Renovation',
            farmer_name: 'Suresh Kumar',
            contact: '9876543211',
            reference: 'REF-002',
            subdivision: 'barwala',
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        },
        {
            sr_no: '003',
            village: 'Hisar',
            constituency: 'Hisar',
            district: 'Hisar',
            rd: 'RD-003',
            minor: 'Minor-C',
            status: 'In Progress',
            work_type: 'Extension',
            farmer_name: 'Rajesh Sharma',
            contact: '9876543212',
            reference: 'REF-003',
            subdivision: 'hisar1',
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        },
        {
            sr_no: '004',
            village: 'Hansi',
            constituency: 'Hansi',
            district: 'Hisar',
            rd: 'RD-004',
            minor: 'Minor-D',
            status: 'Pending',
            work_type: 'New Water Course',
            farmer_name: 'Mohan Lal',
            contact: '9876543213',
            reference: 'REF-004',
            subdivision: 'hisar2',
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        },
        {
            sr_no: '005',
            village: 'Jind',
            constituency: 'Jind',
            district: 'Jind',
            rd: 'RD-005',
            minor: 'Minor-E',
            status: 'Rejected',
            work_type: 'Repair',
            farmer_name: 'Krishan Kumar',
            contact: '9876543214',
            reference: 'REF-005',
            subdivision: 'narwana',
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        }
    ];
    
    for (const application of sampleApplications) {
        await addDoc(collection(db, 'new_applications'), application);
        console.log(`✓ Created application: ${application.sr_no} - ${application.farmer_name}`);
    }
}

// Create sample work status
async function createSampleWorkStatus() {
    const sampleWorks = [
        {
            village: 'Narwana',
            constituency: 'Narwana',
            district: 'Jind',
            water_course: 'Narwana Main Canal',
            work_name: 'Lining of Water Course',
            aa_no_date: 'AA/2024/001 dt. 15-01-2024',
            type_of_work: 'Concrete Lining',
            total_length_to_be_lined: '2000 meters',
            length_lined: '1200 meters',
            contractor: 'ABC Construction',
            remarks: 'Work in progress, 60% completed',
            subdivision: 'narwana',
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        },
        {
            village: 'Barwala',
            constituency: 'Barwala',
            district: 'Hisar',
            water_course: 'Barwala Distribution System',
            work_name: 'Construction of New Outlet',
            aa_no_date: 'AA/2024/002 dt. 20-01-2024',
            type_of_work: 'RCC Outlet',
            total_length_to_be_lined: '500 meters',
            length_lined: '500 meters',
            contractor: 'XYZ Builders',
            remarks: 'Work completed successfully',
            subdivision: 'barwala',
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        },
        {
            village: 'Hisar',
            constituency: 'Hisar',
            district: 'Hisar',
            water_course: 'Hisar Branch Canal',
            work_name: 'Repair and Maintenance',
            aa_no_date: 'AA/2024/003 dt. 25-01-2024',
            type_of_work: 'Brick Lining Repair',
            total_length_to_be_lined: '1500 meters',
            length_lined: '800 meters',
            contractor: 'PQR Engineering',
            remarks: 'Work ongoing, monsoon delayed',
            subdivision: 'hisar1',
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        },
        {
            village: 'Hansi',
            constituency: 'Hansi',
            district: 'Hisar',
            water_course: 'Hansi Minor',
            work_name: 'De-silting and Cleaning',
            aa_no_date: 'AA/2024/004 dt. 30-01-2024',
            type_of_work: 'De-silting',
            total_length_to_be_lined: '3000 meters',
            length_lined: '0 meters',
            contractor: 'LMN Contractors',
            remarks: 'Work not yet started, pending clearance',
            subdivision: 'hisar2',
            createdAt: new Date(),
            createdBy: '<EMAIL>'
        }
    ];
    
    for (const work of sampleWorks) {
        await addDoc(collection(db, 'work_status'), work);
        console.log(`✓ Created work: ${work.work_name} - ${work.village}`);
    }
}

// Make setup function globally accessible
window.setupFirebaseData = setupFirebaseData;

console.log('🔥 Firebase setup script loaded. Call setupFirebaseData() to initialize.');
