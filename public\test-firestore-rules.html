<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Firestore Rules - MICADA</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3>Firestore Rules Test</h3>
                    </div>
                    <div class="card-body">
                        <button id="testRead" class="btn btn-primary">Test Read</button>
                        <button id="testWrite" class="btn btn-success">Test Write</button>
                        <button id="testUpdate" class="btn btn-warning">Test Update</button>
                        <button id="testDelete" class="btn btn-danger">Test Delete</button>
                        <button id="signInBtn" class="btn btn-info">Sign In</button>
                        
                        <div id="output" class="mt-4 p-3 bg-light" style="height: 400px; overflow-y: auto; font-family: monospace; font-size: 12px;">
                            Test output will appear here...<br>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getAuth, GoogleAuthProvider, signInWithPopup, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js";
        import { getFirestore, collection, getDocs, getDoc, doc, addDoc, updateDoc, deleteDoc } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyC4bwhHbZ9oc6mPkzIbQKJyIk9sSiA-S9s",
            authDomain: "micada-division.firebaseapp.com",
            projectId: "micada-division",
            storageBucket: "micada-division.appspot.com",
            messagingSenderId: "363841115848",
            appId: "1:363841115848:web:68ce295c4375e68fe077fd",
            measurementId: "G-1RXZT0K512"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);
        const provider = new GoogleAuthProvider();

        let currentUser = null;
        let testDocId = null;

        function log(message) {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}<br>`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        // Sign in
        document.getElementById('signInBtn').addEventListener('click', async () => {
            try {
                const result = await signInWithPopup(auth, provider);
                currentUser = result.user;
                log('✅ Signed in as: ' + currentUser.email);
            } catch (error) {
                log('❌ Sign in failed: ' + error.message);
            }
        });

        // Test read
        document.getElementById('testRead').addEventListener('click', async () => {
            try {
                log('🔍 Testing read permissions...');
                const snapshot = await getDocs(collection(db, 'new_applications'));
                log(`✅ Read successful: ${snapshot.size} documents`);
            } catch (error) {
                log('❌ Read failed: ' + error.message);
                log('Error code: ' + error.code);
            }
        });

        // Test write
        document.getElementById('testWrite').addEventListener('click', async () => {
            try {
                log('✍️ Testing write permissions...');
                const testData = {
                    sr_no: '999',
                    village: 'Test Village',
                    status: 'In the norms',
                    subdivision: 'test',
                    createdAt: new Date(),
                    createdBy: currentUser ? currentUser.email : 'anonymous',
                    lastUpdated: new Date()
                };
                
                const docRef = await addDoc(collection(db, 'new_applications'), testData);
                testDocId = docRef.id;
                log('✅ Write successful: ' + testDocId);
            } catch (error) {
                log('❌ Write failed: ' + error.message);
                log('Error code: ' + error.code);
            }
        });

        // Test update
        document.getElementById('testUpdate').addEventListener('click', async () => {
            if (!testDocId) {
                log('❌ No test document to update. Create one first.');
                return;
            }
            
            try {
                log('📝 Testing update permissions...');
                await updateDoc(doc(db, 'new_applications', testDocId), {
                    village: 'Updated Test Village',
                    lastUpdated: new Date()
                });
                log('✅ Update successful');
            } catch (error) {
                log('❌ Update failed: ' + error.message);
                log('Error code: ' + error.code);
            }
        });

        // Test delete
        document.getElementById('testDelete').addEventListener('click', async () => {
            if (!testDocId) {
                log('❌ No test document to delete. Create one first.');
                return;
            }
            
            try {
                log('🗑️ Testing delete permissions...');
                await deleteDoc(doc(db, 'new_applications', testDocId));
                log('✅ Delete successful');
                testDocId = null;
            } catch (error) {
                log('❌ Delete failed: ' + error.message);
                log('Error code: ' + error.code);
            }
        });

        // Auth state listener
        onAuthStateChanged(auth, (user) => {
            if (user) {
                currentUser = user;
                log('👤 User signed in: ' + user.email);
            } else {
                currentUser = null;
                log('👤 User signed out');
            }
        });

        log('🔥 Firestore rules test loaded');
        log('Sign in first, then test read/write/update/delete operations');
    </script>
</body>
</html>
