<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Setup - MICADA Division</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0">
                            <i class="fas fa-database me-2"></i>
                            Firebase Database Setup
                        </h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>Setup Instructions</h5>
                            <p>This will automatically create all necessary collections and sample data in your Firebase Firestore database.</p>
                        </div>

                        <div class="mb-4">
                            <h5>What will be created:</h5>
                            <ul class="list-group list-group-flush">
                                <li class="list-group-item">
                                    <i class="fas fa-building text-primary me-2"></i>
                                    <strong>Subdivisions Collection:</strong> 4 subdivision records
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-file-alt text-success me-2"></i>
                                    <strong>New Applications Collection:</strong> 5 sample applications
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-tools text-warning me-2"></i>
                                    <strong>Work Status Collection:</strong> 4 sample work records
                                </li>
                                <li class="list-group-item">
                                    <i class="fas fa-users text-info me-2"></i>
                                    <strong>Users Collection:</strong> Will be created when users sign in
                                </li>
                            </ul>
                        </div>

                        <div class="text-center">
                            <button id="setupBtn" class="btn btn-primary btn-lg" onclick="runSetup()">
                                <i class="fas fa-play me-2"></i>
                                Start Firebase Setup
                            </button>
                        </div>

                        <div id="setupProgress" class="mt-4 d-none">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="mt-2">
                                <small id="progressText" class="text-muted">Initializing...</small>
                            </div>
                        </div>

                        <div id="setupResults" class="mt-4 d-none">
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle me-2"></i>Setup Completed!</h5>
                                <p class="mb-0">Your Firebase database has been initialized with sample data.</p>
                                <hr>
                                <a href="index-firebase.html" class="btn btn-success">
                                    <i class="fas fa-arrow-right me-2"></i>
                                    Go to Application
                                </a>
                            </div>
                        </div>

                        <div id="setupError" class="mt-4 d-none">
                            <div class="alert alert-danger">
                                <h5><i class="fas fa-exclamation-triangle me-2"></i>Setup Failed</h5>
                                <p id="errorMessage">An error occurred during setup.</p>
                                <button class="btn btn-outline-danger" onclick="runSetup()">
                                    <i class="fas fa-redo me-2"></i>Try Again
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Important: Google Cloud Console Setup
                        </h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Before running the setup, make sure you have completed these steps in Google Cloud Console:</strong></p>
                        
                        <div class="accordion" id="setupAccordion">
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#step1">
                                        Step 1: Enable Firebase Authentication
                                    </button>
                                </h2>
                                <div id="step1" class="accordion-collapse collapse show" data-bs-parent="#setupAccordion">
                                    <div class="accordion-body">
                                        <ol>
                                            <li>Go to <a href="https://console.firebase.google.com/" target="_blank">Firebase Console</a></li>
                                            <li>Select your project: <strong>micada-division</strong></li>
                                            <li>Click <strong>"Authentication"</strong> in the left sidebar</li>
                                            <li>Click <strong>"Get started"</strong></li>
                                            <li>Go to <strong>"Sign-in method"</strong> tab</li>
                                            <li>Click on <strong>"Google"</strong></li>
                                            <li>Toggle <strong>"Enable"</strong></li>
                                            <li>Select your project support email</li>
                                            <li>Click <strong>"Save"</strong></li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#step2">
                                        Step 2: Create Firestore Database
                                    </button>
                                </h2>
                                <div id="step2" class="accordion-collapse collapse" data-bs-parent="#setupAccordion">
                                    <div class="accordion-body">
                                        <ol>
                                            <li>In Firebase Console, click <strong>"Firestore Database"</strong></li>
                                            <li>Click <strong>"Create database"</strong></li>
                                            <li>Choose <strong>"Start in test mode"</strong> (for now)</li>
                                            <li>Select location: <strong>asia-south1</strong> (closest to India)</li>
                                            <li>Click <strong>"Done"</strong></li>
                                        </ol>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#step3">
                                        Step 3: Configure Security Rules (Optional)
                                    </button>
                                </h2>
                                <div id="step3" class="accordion-collapse collapse" data-bs-parent="#setupAccordion">
                                    <div class="accordion-body">
                                        <p>For production use, update Firestore rules:</p>
                                        <pre class="bg-light p-3"><code>rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /{document=**} {
      allow read, write: if request.auth != null;
    }
  }
}</code></pre>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script type="module" src="firebase-setup.js"></script>
    
    <script>
        async function runSetup() {
            const setupBtn = document.getElementById('setupBtn');
            const setupProgress = document.getElementById('setupProgress');
            const setupResults = document.getElementById('setupResults');
            const setupError = document.getElementById('setupError');
            const progressBar = document.querySelector('.progress-bar');
            const progressText = document.getElementById('progressText');
            
            // Hide previous results
            setupResults.classList.add('d-none');
            setupError.classList.add('d-none');
            
            // Show progress
            setupProgress.classList.remove('d-none');
            setupBtn.disabled = true;
            
            try {
                // Simulate progress
                progressText.textContent = 'Connecting to Firebase...';
                progressBar.style.width = '25%';
                
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                progressText.textContent = 'Creating collections...';
                progressBar.style.width = '50%';
                
                // Run the actual setup
                await window.setupFirebaseData();
                
                progressText.textContent = 'Finalizing setup...';
                progressBar.style.width = '100%';
                
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // Show success
                setupProgress.classList.add('d-none');
                setupResults.classList.remove('d-none');
                
            } catch (error) {
                console.error('Setup error:', error);
                setupProgress.classList.add('d-none');
                setupError.classList.remove('d-none');
                document.getElementById('errorMessage').textContent = error.message;
                setupBtn.disabled = false;
            }
        }
    </script>
</body>
</html>
