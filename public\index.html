<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MICADA Division Monitoring System</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="styles.css" rel="stylesheet">
</head>

<body>
    <!-- Login Screen -->
    <div id="loginScreen" class="d-flex align-items-center justify-content-center min-vh-100 bg-light">
        <div class="card shadow-lg" style="width: 400px;">
            <div class="card-body text-center p-5">
                <div class="mb-4">
                    <i class="fas fa-water text-primary" style="font-size: 3rem;"></i>
                    <h2 class="mt-3 text-primary">MICADA Division</h2>
                    <p class="text-muted">Monitoring System</p>
                </div>

                <button id="signInBtn" class="btn btn-danger btn-lg w-100">
                    <i class="fab fa-google me-2"></i>
                    Sign in with Google
                </button>

                <div class="mt-4">
                    <small class="text-muted">
                        Only authorized MICADA personnel can access this system
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="mainApp" class="d-none">
        <!-- Header -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-water me-2"></i>
                    MICADA Division Monitoring
                </a>

                <div class="navbar-nav ms-auto">
                    <button id="helpBtn" class="btn btn-outline-light btn-sm me-2" onclick="openHelp()">
                        <i class="fas fa-question-circle me-1"></i>Help
                    </button>
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <span id="userInfo">User</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" id="signOutBtn">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar -->
                <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                    <div class="position-sticky pt-3">
                        <ul class="nav flex-column" id="sidebarNav">
                            <!-- Navigation will be populated by JavaScript -->
                        </ul>
                    </div>
                </nav>

                <!-- Main Content -->
                <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                    <div
                        class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2" id="pageTitle">Dashboard</h1>
                    </div>

                    <!-- Dashboard Content -->
                    <div id="dashboardContent">
                        <div class="row">
                            <div class="col-md-6 col-lg-3 mb-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4 id="newAppsCount">0</h4>
                                                <p class="mb-0">New Applications</p>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-file-alt fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-lg-3 mb-4">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4 id="activeWorksCount">0</h4>
                                                <p class="mb-0">Active Works</p>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-tools fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-success">
                            <h5><i class="fas fa-check-circle me-2"></i>System Status</h5>
                            <p class="mb-0">Firebase authentication and database are working correctly!</p>
                        </div>
                    </div>

                    <!-- New Applications Content -->
                    <div id="newApplicationsContent" class="d-none">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3>New Water Course Applications</h3>
                            <div>
                                <button class="btn btn-primary" onclick="showAddApplicationModal()">
                                    <i class="fas fa-plus me-1"></i>Add Application
                                </button>
                                <button class="btn btn-success" onclick="downloadTemplate('applications')">
                                    <i class="fas fa-download me-1"></i>Download Template
                                </button>
                            </div>
                        </div>

                        <!-- Status Chart -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">Application Status Distribution</h6>
                                    </div>
                                    <div class="card-body">
                                        <div style="height: 250px; position: relative;">
                                            <canvas id="applicationsChart"></canvas>
                                        </div>
                                        <div id="applicationsStats" class="mt-2 text-center">
                                            <small class="text-muted">Loading statistics...</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-secondary text-white">
                                        <h6 class="mb-0">Quick Actions</h6>
                                    </div>
                                    <div class="card-body">
                                        <p class="small text-muted">Use filters below to find specific applications:</p>
                                        <ul class="small">
                                            <li><strong>Search:</strong> Type any text to search across all fields</li>
                                            <li><strong>Status:</strong> Filter by "In the norms" or "Out of norms"</li>
                                            <li id="xenHelp1" style="display: none;"><strong>Subdivision:</strong>
                                                Filter by specific subdivision</li>
                                            <li id="xenHelp2" style="display: none;"><strong>Date:</strong> Filter by
                                                recent updates</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Search and Filter Container -->
                        <div id="searchFilterContainer" class="mb-3">
                            <div class="row">
                                <div class="col-md-3">
                                    <input type="text" class="form-control" id="universalSearch"
                                        placeholder="Search across all fields..."
                                        onkeyup="handleUniversalSearch(event)">
                                </div>
                                <div class="col-md-2" id="xenDateFilter" style="display: none;">
                                    <select class="form-select" id="dateFilterApps"
                                        onchange="filterByDate('applications')">
                                        <option value="">All Time</option>
                                        <option value="1">Last 24 Hours</option>
                                        <option value="7">Last Week</option>
                                        <option value="30">Last Month</option>
                                    </select>
                                </div>
                                <div class="col-md-2" id="statusFilterContainer">
                                    <select class="form-select" id="statusFilterApps"
                                        onchange="filterByStatus('applications')">
                                        <option value="">All Status</option>
                                        <option value="In the norms">In the norms</option>
                                        <option value="Out of norms">Out of norms</option>
                                    </select>
                                </div>
                                <div class="col-md-2" id="subdivisionFilterContainer" style="display: none;">
                                    <select class="form-select" id="subdivisionFilterApps"
                                        onchange="filterBySubdivision('applications')">
                                        <option value="">All Subdivisions</option>
                                        <option value="narwana">Narwana</option>
                                        <option value="barwala">Barwala</option>
                                        <option value="hisar1">Hisar 1</option>
                                        <option value="hisar2">Hisar 2</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-outline-secondary" onclick="refreshApplications()">
                                        <i class="fas fa-refresh"></i> Refresh
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr id="applicationsTableHeader">
                                        <!-- Headers will be populated by JavaScript -->
                                    </tr>
                                </thead>
                                <tbody id="applicationsTableBody">
                                    <!-- Data will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Work Status Content -->
                    <div id="workStatusContent" class="d-none">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3>Work Status Monitoring</h3>
                            <div>
                                <button class="btn btn-primary" onclick="showAddWorkModal()">
                                    <i class="fas fa-plus me-1"></i>Add Work
                                </button>
                                <button class="btn btn-success" onclick="downloadTemplate('works')">
                                    <i class="fas fa-download me-1"></i>Download Template
                                </button>
                            </div>
                        </div>

                        <!-- Status Chart -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-warning text-dark">
                                        <h6 class="mb-0">Work Status Distribution</h6>
                                    </div>
                                    <div class="card-body">
                                        <div style="height: 250px; position: relative;">
                                            <canvas id="worksChart"></canvas>
                                        </div>
                                        <div id="worksStats" class="mt-2 text-center">
                                            <small class="text-muted">Loading statistics...</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header bg-secondary text-white">
                                        <h6 class="mb-0">Quick Actions</h6>
                                    </div>
                                    <div class="card-body">
                                        <p class="small text-muted">Use filters below to find specific work records:</p>
                                        <ul class="small">
                                            <li><strong>Search:</strong> Type any text to search across all fields</li>
                                            <li><strong>Status:</strong> Filter by work status (Ongoing, Completed,
                                                etc.)</li>
                                            <li id="xenHelp3" style="display: none;"><strong>Subdivision:</strong>
                                                Filter by specific subdivision</li>
                                            <li id="xenHelp4" style="display: none;"><strong>Date:</strong> Filter by
                                                recent updates</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Search and Filter Container -->
                        <div id="searchFilterContainer2" class="mb-3">
                            <div class="row">
                                <div class="col-md-3">
                                    <input type="text" class="form-control" id="universalSearchWorks"
                                        placeholder="Search across all fields..."
                                        onkeyup="handleUniversalSearchWorks(event)">
                                </div>
                                <div class="col-md-2" id="xenDateFilter2" style="display: none;">
                                    <select class="form-select" id="dateFilterWorks" onchange="filterByDate('works')">
                                        <option value="">All Time</option>
                                        <option value="1">Last 24 Hours</option>
                                        <option value="7">Last Week</option>
                                        <option value="30">Last Month</option>
                                    </select>
                                </div>
                                <div class="col-md-2" id="statusFilterContainer2">
                                    <select class="form-select" id="statusFilterWorks"
                                        onchange="filterByStatus('works')">
                                        <option value="">All Status</option>
                                        <option value="Ongoing">Ongoing</option>
                                        <option value="Completed">Completed</option>
                                        <option value="Delayed">Delayed</option>
                                        <option value="On Hold">On Hold</option>
                                    </select>
                                </div>
                                <div class="col-md-2" id="subdivisionFilterContainer2" style="display: none;">
                                    <select class="form-select" id="subdivisionFilterWorks"
                                        onchange="filterBySubdivision('works')">
                                        <option value="">All Subdivisions</option>
                                        <option value="narwana">Narwana</option>
                                        <option value="barwala">Barwala</option>
                                        <option value="hisar1">Hisar 1</option>
                                        <option value="hisar2">Hisar 2</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-outline-secondary" onclick="refreshWorkStatus()">
                                        <i class="fas fa-refresh"></i> Refresh
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr id="workStatusTableHeader">
                                        <!-- Headers will be populated by JavaScript -->
                                    </tr>
                                </thead>
                                <tbody id="workStatusTableBody">
                                    <!-- Data will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- User Management Content -->
                    <div id="userManagementContent" class="d-none">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3>User Management</h3>
                            <button class="btn btn-primary" onclick="showAddUserModal()">
                                <i class="fas fa-plus me-1"></i>Add New User
                            </button>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Subdivision</th>
                                        <th>Email</th>
                                        <th>Last Login</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTableBody">
                                    <!-- Data will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Reports Content (XEN only) -->
                    <div id="reportsContent" class="d-none">
                        <h3>Reports & Data Export</h3>

                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header bg-primary text-white">
                                        <h5 class="mb-0">Application Reports</h5>
                                    </div>
                                    <div class="card-body">
                                        <p>Export application data in various formats</p>
                                        <button class="btn btn-success me-2" onclick="exportToExcel('applications')">
                                            <i class="fas fa-file-excel me-1"></i>Export to Excel
                                        </button>
                                        <button class="btn btn-info" onclick="exportToCSV('applications')">
                                            <i class="fas fa-file-csv me-1"></i>Export to CSV
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0">Work Status Reports</h5>
                                    </div>
                                    <div class="card-body">
                                        <p>Export work status data in various formats</p>
                                        <button class="btn btn-success me-2" onclick="exportToExcel('works')">
                                            <i class="fas fa-file-excel me-1"></i>Export to Excel
                                        </button>
                                        <button class="btn btn-info" onclick="exportToCSV('works')">
                                            <i class="fas fa-file-csv me-1"></i>Export to CSV
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header bg-warning text-dark">
                                        <h5 class="mb-0">Subdivision-wise Reports</h5>
                                    </div>
                                    <div class="card-body">
                                        <p>Generate Excel reports by subdivision with status filtering</p>
                                        <select class="form-select mb-2" id="subdivisionFilter">
                                            <option value="">Select Subdivision</option>
                                            <option value="narwana">Narwana</option>
                                            <option value="barwala">Barwala</option>
                                            <option value="hisar1">Hisar 1</option>
                                            <option value="hisar2">Hisar 2</option>
                                        </select>
                                        <select class="form-select mb-3" id="reportStatusFilter">
                                            <option value="">All Status</option>
                                            <option value="In the norms">In the norms</option>
                                            <option value="Out of norms">Out of norms</option>
                                            <option value="Ongoing">Ongoing</option>
                                            <option value="Completed">Completed</option>
                                            <option value="Delayed">Delayed</option>
                                            <option value="On Hold">On Hold</option>
                                        </select>
                                        <button class="btn btn-warning w-100" onclick="generateSubdivisionReport()">
                                            <i class="fas fa-file-excel me-1"></i>Generate Excel Report
                                        </button>
                                        <small class="text-muted">Select subdivision and optional status filter</small>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 mb-4">
                                <div class="card">
                                    <div class="card-header bg-info text-white">
                                        <h5 class="mb-0">Summary Statistics</h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="summaryStats">
                                            <p>Loading statistics...</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>

    <!-- Add Application Modal -->
    <div class="modal fade" id="applicationModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="applicationModalTitle">Add New Application</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="applicationForm">
                        <input type="hidden" id="applicationId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="srNo" class="form-label">Sr. No</label>
                                <input type="text" class="form-control" id="srNo" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="village" class="form-label">Village</label>
                                <input type="text" class="form-control" id="village" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="constituency" class="form-label">Constituency</label>
                                <input type="text" class="form-control" id="constituency" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="district" class="form-label">District</label>
                                <input type="text" class="form-control" id="district" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="rd" class="form-label">RD</label>
                                <input type="text" class="form-control" id="rd" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="minor" class="form-label">Minor</label>
                                <input type="text" class="form-control" id="minor" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" required onchange="toggleReasonField()">
                                    <option value="">Select Status</option>
                                    <option value="In the norms">In the norms</option>
                                    <option value="Out of norms">Out of norms</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="juniorEngineer" class="form-label">Junior Engineer</label>
                                <input type="text" class="form-control" id="juniorEngineer" required>
                            </div>
                            <div class="col-md-12 mb-3" id="reasonField" style="display: none;">
                                <label for="reasonOutOfNorms" class="form-label">Reason for Out of Norms</label>
                                <textarea class="form-control" id="reasonOutOfNorms" rows="3"
                                    placeholder="Please specify the reason for being out of norms..."></textarea>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="workType" class="form-label">Work Type</label>
                                <input type="text" class="form-control" id="workType" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="farmerName" class="form-label">Farmer Name</label>
                                <input type="text" class="form-control" id="farmerName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="contact" class="form-label">Contact</label>
                                <input type="text" class="form-control" id="contact" required>
                            </div>
                            <div class="col-md-12 mb-3">
                                <label for="reference" class="form-label">Reference</label>
                                <input type="text" class="form-control" id="reference" required>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveApplication()">Save Application</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Work Modal -->
    <div class="modal fade" id="workModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="workModalTitle">Add New Work</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="workForm">
                        <input type="hidden" id="workId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="waterCourseRD" class="form-label">Water Course RD</label>
                                <input type="text" class="form-control" id="waterCourseRD" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="channelName" class="form-label">Channel Name</label>
                                <input type="text" class="form-control" id="channelName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="physicalProgress" class="form-label">Physical Progress (%)</label>
                                <input type="number" class="form-control" id="physicalProgress" min="0" max="100"
                                    required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="workStatus" class="form-label">Work Status</label>
                                <select class="form-select" id="workStatus" required>
                                    <option value="">Select Status</option>
                                    <option value="Ongoing">Ongoing</option>
                                    <option value="Completed">Completed</option>
                                    <option value="Delayed">Delayed</option>
                                    <option value="On Hold">On Hold</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="aaNoDate" class="form-label">AA No. & Date</label>
                                <input type="text" class="form-control" id="aaNoDate" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="juniorEngineerWork" class="form-label">Junior Engineer</label>
                                <input type="text" class="form-control" id="juniorEngineerWork" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="workVillage" class="form-label">Village</label>
                                <input type="text" class="form-control" id="workVillage" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="workConstituency" class="form-label">Constituency</label>
                                <input type="text" class="form-control" id="workConstituency" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="workDistrict" class="form-label">District</label>
                                <input type="text" class="form-control" id="workDistrict" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="typeOfWork" class="form-label">Type of Work</label>
                                <input type="text" class="form-control" id="typeOfWork" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="totalLength" class="form-label">Total Length to be Lined</label>
                                <input type="text" class="form-control" id="totalLength" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="lengthLined" class="form-label">Length Lined</label>
                                <input type="text" class="form-control" id="lengthLined" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="contractor" class="form-label">Contractor</label>
                                <input type="text" class="form-control" id="contractor" required>
                            </div>
                            <div class="col-md-12 mb-3">
                                <label for="remarks" class="form-label">Remarks</label>
                                <textarea class="form-control" id="remarks" rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveWork()">Save Work</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm">
                        <div class="mb-3">
                            <label for="userEmail" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="userEmail" required>
                            <div class="form-text">User must have a Google account with this email</div>
                        </div>
                        <div class="mb-3">
                            <label for="userSubdivision" class="form-label">Subdivision</label>
                            <select class="form-select" id="userSubdivision" required>
                                <option value="">Select Subdivision</option>
                                <option value="narwana">MICAD Sub Division, Narwana</option>
                                <option value="barwala">Barwala CAD Subdivision</option>
                                <option value="hisar1">CAD Subdivision 1 Hisar</option>
                                <option value="hisar2">CAD Subdivision 2 Hisar</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="userName" class="form-label">Full Name</label>
                            <input type="text" class="form-control" id="userName" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveNewUser()">Add User</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Subdivision Selection Modal for XEN -->
    <div class="modal fade" id="subdivisionSelectionModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Select Subdivision</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>As XEN, please select which subdivision this data belongs to:</p>
                    <div class="mb-3">
                        <label for="xenSubdivisionSelect" class="form-label">Subdivision</label>
                        <select class="form-select" id="xenSubdivisionSelect" required>
                            <option value="">Select Subdivision</option>
                            <option value="narwana">MICAD Sub Division, Narwana</option>
                            <option value="barwala">Barwala CAD Subdivision</option>
                            <option value="hisar1">CAD Subdivision 1 Hisar</option>
                            <option value="hisar2">CAD Subdivision 2 Hisar</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary"
                        onclick="confirmSubdivisionSelection()">Continue</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getAuth, GoogleAuthProvider, signInWithPopup, signOut, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js";
        import { getFirestore, collection, getDocs, getDoc, doc, setDoc, addDoc, updateDoc, deleteDoc, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyC4bwhHbZ9oc6mPkzIbQKJyIk9sSiA-S9s",
            authDomain: "micada-division.firebaseapp.com",
            projectId: "micada-division",
            storageBucket: "micada-division.appspot.com",
            messagingSenderId: "363841115848",
            appId: "1:363841115848:web:68ce295c4375e68fe077fd",
            measurementId: "G-1RXZT0K512"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);
        const provider = new GoogleAuthProvider();

        // Global variables
        let currentUser = null;
        let userRole = null;
        let userSubdivision = null;

        // Helper functions for collection names
        function getApplicationsCollectionName(subdivision) {
            return `applications_${subdivision}`;
        }

        function getWorkStatusCollectionName(subdivision) {
            return `work_status_${subdivision}`;
        }

        function getAllSubdivisions() {
            return ['narwana', 'barwala', 'hisar1', 'hisar2'];
        }

        // Sign in function
        async function signInWithGoogle() {
            try {
                console.log('🔐 Starting Google Sign-in...');
                const result = await signInWithPopup(auth, provider);
                const user = result.user;
                console.log('✅ User signed in:', user.email);

                await checkUserAuthorization(user);

            } catch (error) {
                console.error('❌ Sign in error:', error);
                alert('Sign in failed: ' + error.message);
            }
        }

        // Check user authorization
        async function checkUserAuthorization(user) {
            const email = user.email;
            console.log('🔍 Checking authorization for:', email);

            try {
                // Check if user is XEN
                if (email === '<EMAIL>') {
                    console.log('✅ XEN user detected');
                    currentUser = user;
                    userRole = 'xen';
                    userSubdivision = null;

                    await saveUserInfo(user, { role: 'xen', subdivision: null, name: 'Executive Engineer MICADA' });
                    showMainApp();
                    return;
                }

                // Check subdivision users
                const authorizedUsersQuery = query(
                    collection(db, 'authorized_users'),
                    where('email', '==', email),
                    where('active', '==', true)
                );
                const querySnapshot = await getDocs(authorizedUsersQuery);

                if (!querySnapshot.empty) {
                    const userDoc = querySnapshot.docs[0];
                    const userData = userDoc.data();

                    currentUser = user;
                    userRole = userData.role;
                    userSubdivision = userData.subdivision;

                    await saveUserInfo(user, userData);
                    showMainApp();
                } else {
                    alert(`Access Denied!\n\nEmail: ${email}\n\nYou are not authorized to access this system.\nPlease contact the MICADA Executive Engineer to get access.`);
                    await signOut(auth);
                }
            } catch (error) {
                console.error('❌ Error checking authorization:', error);
                alert('Error checking user authorization: ' + error.message);
                await signOut(auth);
            }
        }

        // Save user info
        async function saveUserInfo(user, userInfo) {
            try {
                const userData = {
                    uid: user.uid,
                    email: user.email,
                    name: user.displayName || userInfo.name || 'Unknown User',
                    role: userInfo.role,
                    subdivision: userInfo.subdivision,
                    lastLogin: new Date(),
                    photoURL: user.photoURL || null
                };

                const userDoc = doc(db, 'users', user.uid);
                await setDoc(userDoc, userData, { merge: true });
                console.log('✅ User info saved');
            } catch (error) {
                console.error('❌ Error saving user info:', error);
            }
        }

        // Show login screen
        function showLoginScreen() {
            document.getElementById('loginScreen').classList.remove('d-none');
            document.getElementById('mainApp').classList.add('d-none');
        }

        // Show main application
        function showMainApp() {
            console.log('🚀 Showing main application...');
            document.getElementById('loginScreen').classList.add('d-none');
            document.getElementById('mainApp').classList.remove('d-none');

            // Update user info display
            document.getElementById('userInfo').innerHTML = `
                ${currentUser.photoURL ? `<img src="${currentUser.photoURL}" class="rounded-circle me-2" width="24" height="24">` : ''}
                Welcome, ${currentUser.displayName}
            `;

            setupNavigation();
            loadDashboard();
        }

        // Setup navigation
        function setupNavigation() {
            const sidebarNav = document.getElementById('sidebarNav');
            let navItems = [
                { id: 'dashboard', icon: 'fas fa-tachometer-alt', text: 'Dashboard' },
                { id: 'newApplications', icon: 'fas fa-file-alt', text: 'New Applications' },
                { id: 'workStatus', icon: 'fas fa-tools', text: 'Work Status' }
            ];

            if (userRole === 'xen') {
                navItems.push({ id: 'userManagement', icon: 'fas fa-users-cog', text: 'Manage Users' });
                navItems.push({ id: 'reports', icon: 'fas fa-chart-bar', text: 'Reports & Export' });
            }

            sidebarNav.innerHTML = navItems.map(item => `
                <li class="nav-item">
                    <a class="nav-link" href="#" id="nav-${item.id}" onclick="showSection('${item.id}')">
                        <i class="${item.icon}"></i>
                        ${item.text}
                    </a>
                </li>
            `).join('');

            document.getElementById('nav-dashboard').classList.add('active');
        }

        // Show section
        window.showSection = function (section) {
            // Hide all sections
            ['dashboardContent', 'newApplicationsContent', 'workStatusContent', 'userManagementContent', 'reportsContent'].forEach(s => {
                document.getElementById(s).classList.add('d-none');
            });

            // Remove active from all nav
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // Show selected section
            document.getElementById(section + 'Content').classList.remove('d-none');
            document.getElementById('nav-' + section).classList.add('active');

            // Update page title
            const titles = {
                dashboard: 'Dashboard',
                newApplications: 'New Water Course Applications',
                workStatus: 'Work Status Monitoring',
                userManagement: 'User Management',
                reports: 'Reports & Data Export'
            };
            document.getElementById('pageTitle').textContent = titles[section];

            // Load section-specific data
            if (section === 'newApplications') {
                loadNewApplications();
            } else if (section === 'workStatus') {
                loadWorkStatus();
            } else if (section === 'userManagement') {
                loadUserManagement();
            } else if (section === 'reports') {
                loadReports();
            }
        };

        // Load new applications
        async function loadNewApplications() {
            console.log('Loading new applications...');
            try {
                const applications = [];

                if (userRole === 'xen') {
                    // XEN sees all subdivisions - load from all collections
                    const subdivisions = getAllSubdivisions();

                    for (const subdivision of subdivisions) {
                        try {
                            const collectionName = getApplicationsCollectionName(subdivision);
                            const applicationsQuery = query(
                                collection(db, collectionName),
                                orderBy('lastUpdated', 'desc')
                            );
                            const querySnapshot = await getDocs(applicationsQuery);

                            querySnapshot.forEach((doc) => {
                                applications.push({
                                    id: doc.id,
                                    subdivision: subdivision,
                                    ...doc.data()
                                });
                            });
                        } catch (subdivisionError) {
                            console.log(`No data found for ${subdivision}:`, subdivisionError.message);
                        }
                    }

                    // Show XEN filters and help
                    document.getElementById('xenDateFilter').style.display = 'block';
                    document.getElementById('subdivisionFilterContainer').style.display = 'block';
                    document.getElementById('xenHelp1').style.display = 'block';
                    document.getElementById('xenHelp2').style.display = 'block';

                } else {
                    // SDO sees only their subdivision data
                    const collectionName = getApplicationsCollectionName(userSubdivision);
                    const applicationsQuery = query(
                        collection(db, collectionName),
                        orderBy('lastUpdated', 'desc')
                    );
                    const querySnapshot = await getDocs(applicationsQuery);

                    querySnapshot.forEach((doc) => {
                        applications.push({
                            id: doc.id,
                            subdivision: userSubdivision,
                            ...doc.data()
                        });
                    });
                }

                console.log('Applications loaded:', applications.length);
                displayApplicationsTable(applications);

                // Create chart
                createApplicationsChart(applications);

            } catch (error) {
                console.error('Error loading applications:', error);
                // Fallback to createdAt if lastUpdated doesn't exist
                try {
                    const applications = [];

                    if (userRole === 'xen') {
                        const subdivisions = getAllSubdivisions();

                        for (const subdivision of subdivisions) {
                            try {
                                const collectionName = getApplicationsCollectionName(subdivision);
                                const fallbackQuery = query(collection(db, collectionName), orderBy('createdAt', 'desc'));
                                const fallbackSnapshot = await getDocs(fallbackQuery);

                                fallbackSnapshot.forEach((doc) => {
                                    applications.push({
                                        id: doc.id,
                                        subdivision: subdivision,
                                        ...doc.data()
                                    });
                                });
                            } catch (subdivisionError) {
                                console.log(`Fallback failed for ${subdivision}:`, subdivisionError.message);
                            }
                        }
                    } else {
                        const collectionName = getApplicationsCollectionName(userSubdivision);
                        const fallbackQuery = query(collection(db, collectionName), orderBy('createdAt', 'desc'));
                        const fallbackSnapshot = await getDocs(fallbackQuery);

                        fallbackSnapshot.forEach((doc) => {
                            applications.push({
                                id: doc.id,
                                subdivision: userSubdivision,
                                ...doc.data()
                            });
                        });
                    }

                    displayApplicationsTable(applications);
                } catch (fallbackError) {
                    console.error('Fallback query also failed:', fallbackError);
                    alert('Error loading applications: ' + fallbackError.message);
                }
            }
        }

        // Load work status
        async function loadWorkStatus() {
            console.log('Loading work status...');
            try {
                const works = [];

                if (userRole === 'xen') {
                    // XEN sees all subdivisions - load from all collections
                    const subdivisions = getAllSubdivisions();

                    for (const subdivision of subdivisions) {
                        try {
                            const collectionName = getWorkStatusCollectionName(subdivision);
                            const worksQuery = query(
                                collection(db, collectionName),
                                orderBy('lastUpdated', 'desc')
                            );
                            const querySnapshot = await getDocs(worksQuery);

                            querySnapshot.forEach((doc) => {
                                works.push({
                                    id: doc.id,
                                    subdivision: subdivision,
                                    ...doc.data()
                                });
                            });
                        } catch (subdivisionError) {
                            console.log(`No work data found for ${subdivision}:`, subdivisionError.message);
                        }
                    }

                    // Show XEN filters and help
                    document.getElementById('xenDateFilter2').style.display = 'block';
                    document.getElementById('subdivisionFilterContainer2').style.display = 'block';
                    document.getElementById('xenHelp3').style.display = 'block';
                    document.getElementById('xenHelp4').style.display = 'block';

                } else {
                    // SDO sees only their subdivision data
                    const collectionName = getWorkStatusCollectionName(userSubdivision);
                    const worksQuery = query(
                        collection(db, collectionName),
                        orderBy('lastUpdated', 'desc')
                    );
                    const querySnapshot = await getDocs(worksQuery);

                    querySnapshot.forEach((doc) => {
                        works.push({
                            id: doc.id,
                            subdivision: userSubdivision,
                            ...doc.data()
                        });
                    });
                }

                console.log('Works loaded:', works.length);
                displayWorkStatusTable(works);

                // Create chart
                createWorksChart(works);

            } catch (error) {
                console.error('Error loading work status:', error);
                // Fallback to createdAt if lastUpdated doesn't exist
                try {
                    const works = [];

                    if (userRole === 'xen') {
                        const subdivisions = getAllSubdivisions();

                        for (const subdivision of subdivisions) {
                            try {
                                const collectionName = getWorkStatusCollectionName(subdivision);
                                const fallbackQuery = query(collection(db, collectionName), orderBy('createdAt', 'desc'));
                                const fallbackSnapshot = await getDocs(fallbackQuery);

                                fallbackSnapshot.forEach((doc) => {
                                    works.push({
                                        id: doc.id,
                                        subdivision: subdivision,
                                        ...doc.data()
                                    });
                                });
                            } catch (subdivisionError) {
                                console.log(`Fallback failed for ${subdivision}:`, subdivisionError.message);
                            }
                        }
                    } else {
                        const collectionName = getWorkStatusCollectionName(userSubdivision);
                        const fallbackQuery = query(collection(db, collectionName), orderBy('createdAt', 'desc'));
                        const fallbackSnapshot = await getDocs(fallbackQuery);

                        fallbackSnapshot.forEach((doc) => {
                            works.push({
                                id: doc.id,
                                subdivision: userSubdivision,
                                ...doc.data()
                            });
                        });
                    }

                    displayWorkStatusTable(works);
                } catch (fallbackError) {
                    console.error('Fallback query also failed:', fallbackError);
                    alert('Error loading work status: ' + fallbackError.message);
                }
            }
        }

        // Load user management
        async function loadUserManagement() {
            if (userRole !== 'xen') return;

            try {
                const [usersSnapshot, authorizedSnapshot] = await Promise.all([
                    getDocs(collection(db, 'users')),
                    getDocs(collection(db, 'authorized_users'))
                ]);

                const loggedInUsers = [];
                const authorizedUsers = [];

                usersSnapshot.forEach((doc) => {
                    loggedInUsers.push({ id: doc.id, ...doc.data() });
                });

                authorizedSnapshot.forEach((doc) => {
                    authorizedUsers.push({ id: doc.id, ...doc.data() });
                });

                displayUsersTable(loggedInUsers, authorizedUsers);

            } catch (error) {
                console.error('Error loading users:', error);
            }
        }

        // Load dashboard
        async function loadDashboard() {
            try {
                const [appsSnapshot, worksSnapshot] = await Promise.all([
                    getDocs(collection(db, 'new_applications')),
                    getDocs(collection(db, 'work_status'))
                ]);

                document.getElementById('newAppsCount').textContent = appsSnapshot.size;
                document.getElementById('activeWorksCount').textContent = worksSnapshot.size;
            } catch (error) {
                console.error('Error loading dashboard:', error);
            }
        }

        // Display applications table
        function displayApplicationsTable(applications) {
            const headerRow = document.getElementById('applicationsTableHeader');
            const tbody = document.getElementById('applicationsTableBody');

            // Define headers with new columns
            let headers = ['Sr. No', 'Village', 'Constituency', 'District', 'RD', 'Minor', 'Status', 'Reason', 'Work Type', 'Farmer Name', 'Contact', 'Reference', 'Junior Engineer', 'Last Updated'];

            if (userRole === 'xen') {
                headers.push('Subdivision');
                headers.push('Actions');
            } else {
                headers.push('Actions');
            }

            // Build header
            headerRow.innerHTML = headers.map(header => `<th>${header}</th>`).join('');

            // Build table body
            tbody.innerHTML = applications.map(app => {
                const lastUpdated = app.lastUpdated ? new Date(app.lastUpdated.seconds * 1000).toLocaleDateString() :
                    app.createdAt ? new Date(app.createdAt.seconds * 1000).toLocaleDateString() : '';

                let row = `
                    <tr>
                        <td>${app.sr_no || ''}</td>
                        <td>${app.village || ''}</td>
                        <td>${app.constituency || ''}</td>
                        <td>${app.district || ''}</td>
                        <td>${app.rd || ''}</td>
                        <td>${app.minor || ''}</td>
                        <td><span class="badge ${getStatusBadgeClass(app.status)}">${app.status || ''}</span></td>
                        <td>${app.reason_out_of_norms || ''}</td>
                        <td>${app.work_type || ''}</td>
                        <td>${app.farmer_name || ''}</td>
                        <td>${app.contact || ''}</td>
                        <td>${app.reference || ''}</td>
                        <td>${app.junior_engineer || ''}</td>
                        <td>${lastUpdated}</td>
                `;

                if (userRole === 'xen') {
                    row += `<td>${app.subdivision || ''}</td>`;
                }

                // Add action buttons
                row += `
                    <td>
                        <button class="btn btn-sm btn-outline-primary me-1" onclick="editApplication('${app.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteApplication('${app.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;

                row += '</tr>';
                return row;
            }).join('');
        }

        // Display work status table
        function displayWorkStatusTable(works) {
            const headerRow = document.getElementById('workStatusTableHeader');
            const tbody = document.getElementById('workStatusTableBody');

            // Define headers with new columns
            let headers = ['Water Course RD', 'Channel Name', 'Physical Progress (%)', 'Status', 'AA No. & Date', 'Type of Work', 'Total Length', 'Length Lined', 'Contractor', 'Junior Engineer', 'Remarks', 'Last Updated', 'Village', 'Constituency', 'District'];

            if (userRole === 'xen') {
                headers.push('Subdivision');
                headers.push('Actions');
            } else {
                headers.push('Actions');
            }

            // Build header
            headerRow.innerHTML = headers.map(header => `<th>${header}</th>`).join('');

            // Build table body
            tbody.innerHTML = works.map(work => {
                const lastUpdated = work.lastUpdated ? new Date(work.lastUpdated.seconds * 1000).toLocaleDateString() :
                    work.createdAt ? new Date(work.createdAt.seconds * 1000).toLocaleDateString() : '';

                let row = `
                    <tr>
                        <td>${work.water_course_rd || work.water_course || ''}</td>
                        <td>${work.channel_name || work.work_name || ''}</td>
                        <td>${work.physical_progress ? work.physical_progress + '%' : '0%'}</td>
                        <td><span class="badge ${getWorkStatusBadgeClass(work.work_status)}">${work.work_status || 'Not Set'}</span></td>
                        <td>${work.aa_no_date || ''}</td>
                        <td>${work.type_of_work || ''}</td>
                        <td>${work.total_length_to_be_lined || ''}</td>
                        <td>${work.length_lined || ''}</td>
                        <td>${work.contractor || ''}</td>
                        <td>${work.junior_engineer || ''}</td>
                        <td>${work.remarks || ''}</td>
                        <td>${lastUpdated}</td>
                        <td>${work.village || ''}</td>
                        <td>${work.constituency || ''}</td>
                        <td>${work.district || ''}</td>
                `;

                if (userRole === 'xen') {
                    row += `<td>${work.subdivision || ''}</td>`;
                }

                // Add action buttons
                row += `
                    <td>
                        <button class="btn btn-sm btn-outline-primary me-1" onclick="editWork('${work.id}')">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteWork('${work.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                `;

                row += '</tr>';
                return row;
            }).join('');
        }

        // Display users table
        function displayUsersTable(loggedInUsers, authorizedUsers) {
            const tbody = document.getElementById('usersTableBody');

            // Combine and organize user data
            const userMap = new Map();

            // Add authorized users first
            authorizedUsers.forEach(authUser => {
                userMap.set(authUser.email, {
                    email: authUser.email,
                    subdivision: authUser.subdivision,
                    role: authUser.role,
                    name: authUser.name,
                    active: authUser.active,
                    lastLogin: null,
                    hasLoggedIn: false
                });
            });

            // Update with login information
            loggedInUsers.forEach(loggedUser => {
                if (userMap.has(loggedUser.email)) {
                    const existing = userMap.get(loggedUser.email);
                    existing.lastLogin = loggedUser.lastLogin;
                    existing.hasLoggedIn = true;
                    existing.photoURL = loggedUser.photoURL;
                }
            });

            tbody.innerHTML = Array.from(userMap.values()).map(user => `
                <tr>
                    <td>${user.subdivision || 'XEN Office'}</td>
                    <td>
                        ${user.photoURL ? `<img src="${user.photoURL}" class="rounded-circle me-2" width="24" height="24">` : ''}
                        ${user.email}
                    </td>
                    <td>${user.lastLogin ? new Date(user.lastLogin.seconds * 1000).toLocaleDateString() : 'Never'}</td>
                    <td>
                        <span class="badge ${user.active ? 'bg-success' : 'bg-danger'}">
                            ${user.active ? 'Active' : 'Inactive'}
                        </span>
                        ${user.hasLoggedIn ? '<span class="badge bg-info ms-1">Logged In</span>' : ''}
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary me-1" onclick="editUserAccess('${user.email}')">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="btn btn-sm ${user.active ? 'btn-outline-danger' : 'btn-outline-success'}"
                                onclick="toggleUserAccess('${user.email}', ${!user.active})">
                            <i class="fas ${user.active ? 'fa-ban' : 'fa-check'}"></i>
                            ${user.active ? 'Disable' : 'Enable'}
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // Get status badge class
        function getStatusBadgeClass(status) {
            if (!status) return 'bg-secondary';
            const statusLower = status.toLowerCase();
            if (statusLower.includes('approved') || statusLower.includes('completed')) return 'bg-success';
            if (statusLower.includes('rejected')) return 'bg-danger';
            if (statusLower.includes('progress')) return 'bg-warning';
            return 'bg-primary';
        }

        // Get work status badge class
        function getWorkStatusBadgeClass(status) {
            if (!status) return 'bg-secondary';
            const statusLower = status.toLowerCase();
            if (statusLower.includes('completed')) return 'bg-success';
            if (statusLower.includes('delayed') || statusLower.includes('hold')) return 'bg-danger';
            if (statusLower.includes('ongoing')) return 'bg-primary';
            return 'bg-warning';
        }

        // Modal functions
        window.showAddApplicationModal = async function () {
            // For XEN users, show subdivision selection first
            if (userRole === 'xen') {
                const subdivision = await showSubdivisionSelectionModal();
                if (!subdivision) return;

                // Set temporary subdivision for this entry
                window.tempSubdivision = subdivision;
            }

            document.getElementById('applicationForm').reset();
            document.getElementById('applicationId').value = '';
            document.getElementById('applicationModalTitle').textContent = 'Add New Application';

            // Hide reason field initially
            document.getElementById('reasonField').style.display = 'none';

            // Auto-generate next Sr. Number
            await generateNextSrNumber();

            const modal = new bootstrap.Modal(document.getElementById('applicationModal'));
            modal.show();
        };

        window.showAddWorkModal = async function () {
            // For XEN users, show subdivision selection first
            if (userRole === 'xen') {
                const subdivision = await showSubdivisionSelectionModal();
                if (!subdivision) return;

                // Set temporary subdivision for this entry
                window.tempSubdivision = subdivision;
            }

            document.getElementById('workForm').reset();
            document.getElementById('workId').value = '';
            document.getElementById('workModalTitle').textContent = 'Add New Work';
            const modal = new bootstrap.Modal(document.getElementById('workModal'));
            modal.show();
        };

        // Generate next Sr. Number
        async function generateNextSrNumber() {
            try {
                const targetSubdivision = window.tempSubdivision || userSubdivision;
                const applicationsQuery = query(
                    collection(db, 'new_applications'),
                    where('subdivision', '==', targetSubdivision)
                );
                const querySnapshot = await getDocs(applicationsQuery);

                let maxSrNo = 0;
                querySnapshot.forEach((doc) => {
                    const data = doc.data();
                    if (data.sr_no) {
                        // Extract number from sr_no (handle both "1" and "001/2024" formats)
                        const numMatch = data.sr_no.toString().match(/(\d+)/);
                        if (numMatch) {
                            const num = parseInt(numMatch[1]);
                            if (num > maxSrNo) {
                                maxSrNo = num;
                            }
                        }
                    }
                });

                const nextSrNo = maxSrNo + 1;
                document.getElementById('srNo').value = nextSrNo.toString();

            } catch (error) {
                console.error('Error generating Sr. Number:', error);
                document.getElementById('srNo').value = '1';
            }
        }

        window.showAddUserModal = function () {
            if (userRole !== 'xen') {
                alert('Access denied. Only XEN can manage users.');
                return;
            }
            document.getElementById('addUserForm').reset();
            const modal = new bootstrap.Modal(document.getElementById('addUserModal'));
            modal.show();
        };

        // Save functions
        window.saveApplication = async function () {
            const applicationId = document.getElementById('applicationId').value;
            const isEdit = applicationId && applicationId.trim() !== '';

            const formData = {
                sr_no: document.getElementById('srNo').value,
                village: document.getElementById('village').value,
                constituency: document.getElementById('constituency').value,
                district: document.getElementById('district').value,
                rd: document.getElementById('rd').value,
                minor: document.getElementById('minor').value,
                status: document.getElementById('status').value,
                reason_out_of_norms: document.getElementById('reasonOutOfNorms').value,
                work_type: document.getElementById('workType').value,
                farmer_name: document.getElementById('farmerName').value,
                contact: document.getElementById('contact').value,
                reference: document.getElementById('reference').value,
                junior_engineer: document.getElementById('juniorEngineer').value,
                subdivision: window.tempSubdivision || userSubdivision || 'xen',
                lastUpdated: new Date()
            };

            try {
                const targetSubdivision = window.editTargetSubdivision || window.tempSubdivision || userSubdivision;
                const collectionName = getApplicationsCollectionName(targetSubdivision);

                if (isEdit) {
                    // Update existing application
                    await updateDoc(doc(db, collectionName, applicationId), formData);
                    alert('Application updated successfully!');
                } else {
                    // Create new application
                    formData.createdAt = new Date();
                    formData.createdBy = currentUser.email;
                    await addDoc(collection(db, collectionName), formData);
                    alert('Application saved successfully!');
                }

                const modal = bootstrap.Modal.getInstance(document.getElementById('applicationModal'));
                modal.hide();

                // Clear temp subdivision and edit target
                window.tempSubdivision = null;
                window.editTargetSubdivision = null;

                loadNewApplications();
            } catch (error) {
                console.error('Error saving application:', error);
                alert('Error saving application: ' + error.message);
            }
        };

        window.saveWork = async function () {
            const workId = document.getElementById('workId').value;
            const isEdit = workId && workId.trim() !== '';

            const formData = {
                water_course_rd: document.getElementById('waterCourseRD').value,
                channel_name: document.getElementById('channelName').value,
                physical_progress: document.getElementById('physicalProgress').value,
                work_status: document.getElementById('workStatus').value,
                aa_no_date: document.getElementById('aaNoDate').value,
                type_of_work: document.getElementById('typeOfWork').value,
                total_length_to_be_lined: document.getElementById('totalLength').value,
                length_lined: document.getElementById('lengthLined').value,
                contractor: document.getElementById('contractor').value,
                junior_engineer: document.getElementById('juniorEngineerWork').value,
                remarks: document.getElementById('remarks').value,
                village: document.getElementById('workVillage').value,
                constituency: document.getElementById('workConstituency').value,
                district: document.getElementById('workDistrict').value,
                lastUpdated: new Date()
            };

            try {
                const targetSubdivision = window.editTargetSubdivision || window.tempSubdivision || userSubdivision;
                const collectionName = getWorkStatusCollectionName(targetSubdivision);

                if (isEdit) {
                    // Update existing work
                    await updateDoc(doc(db, collectionName, workId), formData);
                    alert('Work updated successfully!');
                } else {
                    // Create new work
                    formData.createdAt = new Date();
                    formData.createdBy = currentUser.email;
                    await addDoc(collection(db, collectionName), formData);
                    alert('Work saved successfully!');
                }

                const modal = bootstrap.Modal.getInstance(document.getElementById('workModal'));
                modal.hide();

                // Clear temp subdivision and edit target
                window.tempSubdivision = null;
                window.editTargetSubdivision = null;

                loadWorkStatus();
            } catch (error) {
                console.error('Error saving work:', error);
                alert('Error saving work: ' + error.message);
            }
        };

        window.saveNewUser = async function () {
            if (userRole !== 'xen') {
                alert('Access denied. Only XEN can manage users.');
                return;
            }

            const email = document.getElementById('userEmail').value;
            const subdivision = document.getElementById('userSubdivision').value;
            const name = document.getElementById('userName').value;

            if (!email || !subdivision || !name) {
                alert('Please fill all fields');
                return;
            }

            try {
                const existingQuery = query(collection(db, 'authorized_users'), where('email', '==', email));
                const existingSnapshot = await getDocs(existingQuery);

                if (!existingSnapshot.empty) {
                    alert('User with this email already exists');
                    return;
                }

                const newUser = {
                    email: email,
                    role: 'subdivision',
                    subdivision: subdivision,
                    name: name,
                    active: true,
                    createdAt: new Date(),
                    createdBy: '<EMAIL>'
                };

                await addDoc(collection(db, 'authorized_users'), newUser);
                alert('User added successfully!');

                const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
                modal.hide();
                loadUserManagement();

            } catch (error) {
                console.error('Error adding user:', error);
                alert('Error adding user: ' + error.message);
            }
        };

        // Utility functions
        window.downloadTemplate = function (type) {
            let headers, filename;

            if (type === 'applications') {
                headers = ['Sr. No', 'Village', 'Constituency', 'District', 'RD', 'Minor', 'Status', 'Work Type', 'Farmer Name', 'Contact', 'Reference'];
                filename = 'new_applications_template.csv';
            } else if (type === 'works') {
                headers = ['Village', 'Constituency', 'District', 'Water Course', 'Work Name', 'AA No. & Date', 'Type of Work', 'Total Length', 'Length Lined', 'Contractor', 'Remarks'];
                filename = 'work_status_template.csv';
            }

            const csvContent = headers.join(',') + '\n';
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        };

        window.handleUniversalSearch = function (event) {
            const searchTerm = event.target.value.toLowerCase();
            const tableRows = document.querySelectorAll('#applicationsTableBody tr');

            tableRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        };

        window.handleUniversalSearchWorks = function (event) {
            const searchTerm = event.target.value.toLowerCase();
            const tableRows = document.querySelectorAll('#workStatusTableBody tr');

            tableRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        };

        window.editUserAccess = function (email) {
            alert('Edit user functionality - Email: ' + email);
        };

        window.toggleUserAccess = function (email, newStatus) {
            alert('Toggle user access functionality - Email: ' + email + ', New Status: ' + newStatus);
        };

        // Reports and Export Functions
        async function loadReports() {
            if (userRole !== 'xen') return;

            try {
                const [appsSnapshot, worksSnapshot, usersSnapshot] = await Promise.all([
                    getDocs(collection(db, 'new_applications')),
                    getDocs(collection(db, 'work_status')),
                    getDocs(collection(db, 'authorized_users'))
                ]);

                const stats = {
                    totalApplications: appsSnapshot.size,
                    totalWorks: worksSnapshot.size,
                    totalUsers: usersSnapshot.size,
                    subdivisionStats: {}
                };

                appsSnapshot.forEach(doc => {
                    const data = doc.data();
                    const subdivision = data.subdivision || 'unknown';
                    if (!stats.subdivisionStats[subdivision]) {
                        stats.subdivisionStats[subdivision] = { applications: 0, works: 0 };
                    }
                    stats.subdivisionStats[subdivision].applications++;
                });

                worksSnapshot.forEach(doc => {
                    const data = doc.data();
                    const subdivision = data.subdivision || 'unknown';
                    if (!stats.subdivisionStats[subdivision]) {
                        stats.subdivisionStats[subdivision] = { applications: 0, works: 0 };
                    }
                    stats.subdivisionStats[subdivision].works++;
                });

                displaySummaryStats(stats);

            } catch (error) {
                console.error('Error loading reports:', error);
            }
        }

        function displaySummaryStats(stats) {
            const summaryDiv = document.getElementById('summaryStats');

            let html = `
                <div class="row">
                    <div class="col-md-4 mb-2">
                        <strong>Total Applications:</strong> ${stats.totalApplications}
                    </div>
                    <div class="col-md-4 mb-2">
                        <strong>Total Works:</strong> ${stats.totalWorks}
                    </div>
                    <div class="col-md-4 mb-2">
                        <strong>Total Users:</strong> ${stats.totalUsers}
                    </div>
                </div>
                <hr>
                <h6>Subdivision-wise Statistics:</h6>
            `;

            Object.keys(stats.subdivisionStats).forEach(subdivision => {
                const data = stats.subdivisionStats[subdivision];
                html += `
                    <div class="row mb-1">
                        <div class="col-md-4"><strong>${subdivision}:</strong></div>
                        <div class="col-md-4">Apps: ${data.applications}</div>
                        <div class="col-md-4">Works: ${data.works}</div>
                    </div>
                `;
            });

            summaryDiv.innerHTML = html;
        }

        window.exportToExcel = async function (type) {
            if (userRole !== 'xen') {
                alert('Access denied. Only XEN can export data.');
                return;
            }

            try {
                let data, filename, headers;

                if (type === 'applications') {
                    const querySnapshot = await getDocs(collection(db, 'new_applications'));
                    data = [];
                    querySnapshot.forEach(doc => {
                        data.push(doc.data());
                    });
                    headers = ['Sr. No', 'Village', 'Constituency', 'District', 'RD', 'Minor', 'Status', 'Work Type', 'Farmer Name', 'Contact', 'Reference', 'Subdivision'];
                    filename = 'MICADA_Applications_Export.csv';
                } else if (type === 'works') {
                    const querySnapshot = await getDocs(collection(db, 'work_status'));
                    data = [];
                    querySnapshot.forEach(doc => {
                        data.push(doc.data());
                    });
                    headers = ['Water Course RD', 'Channel Name', 'Physical Progress (%)', 'Type of Work', 'Total Length', 'Length Lined', 'Contractor', 'Remarks', 'Village', 'Constituency', 'District', 'Subdivision'];
                    filename = 'MICADA_WorkStatus_Export.csv';
                }

                exportDataToCSV(data, headers, filename, type);

            } catch (error) {
                console.error('Error exporting data:', error);
                alert('Error exporting data: ' + error.message);
            }
        };

        window.exportToCSV = function (type) {
            exportToExcel(type);
        };

        window.generateSubdivisionReport = async function () {
            const subdivision = document.getElementById('subdivisionFilter').value;
            const statusFilter = document.getElementById('reportStatusFilter').value;

            if (!subdivision) {
                alert('Please select a subdivision');
                return;
            }

            try {
                // Load data from subdivision-specific collections
                const appsCollectionName = getApplicationsCollectionName(subdivision);
                const worksCollectionName = getWorkStatusCollectionName(subdivision);

                const [appsSnapshot, worksSnapshot] = await Promise.all([
                    getDocs(collection(db, appsCollectionName)),
                    getDocs(collection(db, worksCollectionName))
                ]);

                const appsData = [];
                const worksData = [];

                appsSnapshot.forEach(doc => appsData.push(doc.data()));
                worksSnapshot.forEach(doc => worksData.push(doc.data()));

                // Apply status filter if selected
                if (statusFilter) {
                    if (statusFilter === 'In the norms' || statusFilter === 'Out of norms') {
                        appsData = appsData.filter(app => app.status === statusFilter);
                    } else {
                        worksData = worksData.filter(work => work.work_status === statusFilter);
                    }
                }

                // Generate Excel file
                await generateExcelReport(subdivision, appsData, worksData, statusFilter);

            } catch (error) {
                console.error('Error generating report:', error);
                alert('Error generating report: ' + error.message);
            }
        };

        // Generate Excel report with proper formatting
        async function generateExcelReport(subdivision, appsData, worksData, statusFilter = '') {
            // Create CSV content with proper Excel formatting
            let csvContent = '';

            // Report header with bold formatting (using UTF-8 BOM for Excel compatibility)
            csvContent = '\uFEFF'; // UTF-8 BOM
            csvContent += `MICADA Division - ${subdivision.toUpperCase()} Subdivision Report\n`;
            csvContent += `Generated on: ${new Date().toLocaleString()}\n`;
            if (statusFilter) {
                csvContent += `Status Filter: ${statusFilter}\n`;
            }
            csvContent += `\n`;

            // Applications section
            csvContent += `WATER COURSE APPLICATIONS (${appsData.length} records)\n`;
            csvContent += `Sr. No,Village,Constituency,District,RD,Minor,Status,Reason,Work Type,Farmer Name,Contact,Reference,Junior Engineer,Last Updated\n`;

            appsData.forEach((app, index) => {
                const row = [
                    index + 1, // Adjusted Sr. Number for report
                    app.village || '',
                    app.constituency || '',
                    app.district || '',
                    app.rd || '',
                    app.minor || '',
                    app.status || '',
                    app.reason_out_of_norms || '',
                    app.work_type || '',
                    app.farmer_name || '',
                    app.contact || '',
                    app.reference || '',
                    app.junior_engineer || '',
                    app.lastUpdated ? new Date(app.lastUpdated.seconds * 1000).toLocaleDateString() : ''
                ];

                // Escape commas and quotes for CSV
                const escapedRow = row.map(field => {
                    if (typeof field === 'string' && (field.includes(',') || field.includes('"'))) {
                        return '"' + field.replace(/"/g, '""') + '"';
                    }
                    return field;
                });

                csvContent += escapedRow.join(',') + '\n';
            });

            csvContent += `\n`;

            // Work Status section
            csvContent += `WORK STATUS MONITORING (${worksData.length} records)\n`;
            csvContent += `Sr. No,Water Course RD,Channel Name,Physical Progress (%),Status,AA No. & Date,Type of Work,Total Length,Length Lined,Contractor,Junior Engineer,Remarks,Village,Constituency,District,Last Updated\n`;

            worksData.forEach((work, index) => {
                const row = [
                    index + 1, // Adjusted Sr. Number for report
                    work.water_course_rd || '',
                    work.channel_name || '',
                    work.physical_progress ? work.physical_progress + '%' : '0%',
                    work.work_status || '',
                    work.aa_no_date || '',
                    work.type_of_work || '',
                    work.total_length_to_be_lined || '',
                    work.length_lined || '',
                    work.contractor || '',
                    work.junior_engineer || '',
                    work.remarks || '',
                    work.village || '',
                    work.constituency || '',
                    work.district || '',
                    work.lastUpdated ? new Date(work.lastUpdated.seconds * 1000).toLocaleDateString() : ''
                ];

                // Escape commas and quotes for CSV
                const escapedRow = row.map(field => {
                    if (typeof field === 'string' && (field.includes(',') || field.includes('"'))) {
                        return '"' + field.replace(/"/g, '""') + '"';
                    }
                    return field;
                });

                csvContent += escapedRow.join(',') + '\n';
            });

            // Summary section
            csvContent += `\n`;
            csvContent += `SUMMARY\n`;
            csvContent += `Total Applications,${appsData.length}\n`;
            csvContent += `Total Works,${worksData.length}\n`;

            // Create and download file
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;

            const statusSuffix = statusFilter ? `_${statusFilter.replace(/\s+/g, '_')}` : '';
            a.download = `MICADA_${subdivision}_Report${statusSuffix}.csv`;

            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            alert(`Excel report generated successfully!\nApplications: ${appsData.length}\nWorks: ${worksData.length}`);
        }

        function exportDataToCSV(data, headers, filename, type) {
            let csvContent = headers.join(',') + '\n';

            data.forEach(item => {
                let row = [];

                if (type === 'applications') {
                    row = [
                        item.sr_no || '',
                        item.village || '',
                        item.constituency || '',
                        item.district || '',
                        item.rd || '',
                        item.minor || '',
                        item.status || '',
                        item.work_type || '',
                        item.farmer_name || '',
                        item.contact || '',
                        item.reference || '',
                        item.subdivision || ''
                    ];
                } else if (type === 'works') {
                    row = [
                        item.water_course_rd || item.water_course || '',
                        item.channel_name || item.work_name || '',
                        item.physical_progress ? item.physical_progress + '%' : '0%',
                        item.type_of_work || '',
                        item.total_length_to_be_lined || '',
                        item.length_lined || '',
                        item.contractor || '',
                        item.remarks || '',
                        item.village || '',
                        item.constituency || '',
                        item.district || '',
                        item.subdivision || ''
                    ];
                }

                row = row.map(field => {
                    if (typeof field === 'string' && (field.includes(',') || field.includes('"'))) {
                        return '"' + field.replace(/"/g, '""') + '"';
                    }
                    return field;
                });

                csvContent += row.join(',') + '\n';
            });

            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            alert(`Data exported successfully: ${filename}`);
        }

        // Additional utility functions
        window.toggleReasonField = function () {
            const status = document.getElementById('status').value;
            const reasonField = document.getElementById('reasonField');

            if (status === 'Out of norms') {
                reasonField.style.display = 'block';
                document.getElementById('reasonOutOfNorms').required = true;
            } else {
                reasonField.style.display = 'none';
                document.getElementById('reasonOutOfNorms').required = false;
                document.getElementById('reasonOutOfNorms').value = '';
            }
        };

        // Subdivision selection for XEN
        function showSubdivisionSelectionModal() {
            return new Promise((resolve) => {
                window.subdivisionSelectionResolve = resolve;
                const modal = new bootstrap.Modal(document.getElementById('subdivisionSelectionModal'));
                modal.show();
            });
        }

        window.confirmSubdivisionSelection = function () {
            const subdivision = document.getElementById('xenSubdivisionSelect').value;
            if (!subdivision) {
                alert('Please select a subdivision');
                return;
            }

            const modal = bootstrap.Modal.getInstance(document.getElementById('subdivisionSelectionModal'));
            modal.hide();

            if (window.subdivisionSelectionResolve) {
                window.subdivisionSelectionResolve(subdivision);
            }
        };

        // Edit and Delete functions
        window.editApplication = async function (appId) {
            try {
                console.log('Editing application:', appId);

                // Get the application data - need to find which collection it's in
                let appSnapshot = null;
                let targetSubdivision = null;

                if (userRole === 'xen') {
                    // XEN can edit from any subdivision - need to find which one
                    const subdivisions = getAllSubdivisions();
                    for (const subdivision of subdivisions) {
                        try {
                            const collectionName = getApplicationsCollectionName(subdivision);
                            const appDoc = doc(db, collectionName, appId);
                            const snapshot = await getDoc(appDoc);
                            if (snapshot.exists()) {
                                appSnapshot = snapshot;
                                targetSubdivision = subdivision;
                                break;
                            }
                        } catch (error) {
                            continue;
                        }
                    }
                } else {
                    // SDO can only edit from their subdivision
                    const collectionName = getApplicationsCollectionName(userSubdivision);
                    const appDoc = doc(db, collectionName, appId);
                    appSnapshot = await getDoc(appDoc);
                    targetSubdivision = userSubdivision;
                }

                if (!appSnapshot || !appSnapshot.exists()) {
                    alert('Application not found!');
                    return;
                }

                const appData = appSnapshot.data();

                // Store the target subdivision for saving
                window.editTargetSubdivision = targetSubdivision;

                // Populate the form with existing data
                document.getElementById('applicationId').value = appId;
                document.getElementById('srNo').value = appData.sr_no || '';
                document.getElementById('village').value = appData.village || '';
                document.getElementById('constituency').value = appData.constituency || '';
                document.getElementById('district').value = appData.district || '';
                document.getElementById('rd').value = appData.rd || '';
                document.getElementById('minor').value = appData.minor || '';
                document.getElementById('status').value = appData.status || '';
                document.getElementById('reasonOutOfNorms').value = appData.reason_out_of_norms || '';
                document.getElementById('workType').value = appData.work_type || '';
                document.getElementById('farmerName').value = appData.farmer_name || '';
                document.getElementById('contact').value = appData.contact || '';
                document.getElementById('reference').value = appData.reference || '';
                document.getElementById('juniorEngineer').value = appData.junior_engineer || '';

                // Show/hide reason field based on status
                toggleReasonField();

                // Update modal title
                document.getElementById('applicationModalTitle').textContent = 'Edit Application';

                // Show the modal
                const modal = new bootstrap.Modal(document.getElementById('applicationModal'));
                modal.show();

            } catch (error) {
                console.error('Error editing application:', error);
                alert('Error loading application data: ' + error.message);
            }
        };

        window.deleteApplication = async function (appId) {
            try {
                console.log('Deleting application:', appId);

                // Get the application data - need to find which collection it's in
                let appSnapshot = null;
                let targetSubdivision = null;

                if (userRole === 'xen') {
                    // XEN can delete from any subdivision - need to find which one
                    const subdivisions = getAllSubdivisions();
                    for (const subdivision of subdivisions) {
                        try {
                            const collectionName = getApplicationsCollectionName(subdivision);
                            const appDoc = doc(db, collectionName, appId);
                            const snapshot = await getDoc(appDoc);
                            if (snapshot.exists()) {
                                appSnapshot = snapshot;
                                targetSubdivision = subdivision;
                                break;
                            }
                        } catch (error) {
                            continue;
                        }
                    }
                } else {
                    // SDO can only delete from their subdivision
                    const collectionName = getApplicationsCollectionName(userSubdivision);
                    const appDoc = doc(db, collectionName, appId);
                    appSnapshot = await getDoc(appDoc);
                    targetSubdivision = userSubdivision;
                }

                if (!appSnapshot || !appSnapshot.exists()) {
                    alert('Application not found!');
                    return;
                }

                const appData = appSnapshot.data();

                if (confirm(`Are you sure you want to delete this application?\n\nSr. No: ${appData.sr_no}\nVillage: ${appData.village}\nFarmer: ${appData.farmer_name}\n\nThis action cannot be undone!`)) {
                    const collectionName = getApplicationsCollectionName(targetSubdivision);
                    await deleteDoc(doc(db, collectionName, appId));
                    alert('Application deleted successfully!');
                    loadNewApplications(); // Refresh the table
                }

            } catch (error) {
                console.error('Error deleting application:', error);
                alert('Error deleting application: ' + error.message);
            }
        };

        window.editWork = async function (workId) {
            try {
                console.log('Editing work:', workId);

                // Get the work data - need to find which collection it's in
                let workSnapshot = null;
                let targetSubdivision = null;

                if (userRole === 'xen') {
                    // XEN can edit from any subdivision - need to find which one
                    const subdivisions = getAllSubdivisions();
                    for (const subdivision of subdivisions) {
                        try {
                            const collectionName = getWorkStatusCollectionName(subdivision);
                            const workDoc = doc(db, collectionName, workId);
                            const snapshot = await getDoc(workDoc);
                            if (snapshot.exists()) {
                                workSnapshot = snapshot;
                                targetSubdivision = subdivision;
                                break;
                            }
                        } catch (error) {
                            continue;
                        }
                    }
                } else {
                    // SDO can only edit from their subdivision
                    const collectionName = getWorkStatusCollectionName(userSubdivision);
                    const workDoc = doc(db, collectionName, workId);
                    workSnapshot = await getDoc(workDoc);
                    targetSubdivision = userSubdivision;
                }

                if (!workSnapshot || !workSnapshot.exists()) {
                    alert('Work record not found!');
                    return;
                }

                const workData = workSnapshot.data();

                // Store the target subdivision for saving
                window.editTargetSubdivision = targetSubdivision;

                // Populate the form with existing data
                document.getElementById('workId').value = workId;
                document.getElementById('waterCourseRD').value = workData.water_course_rd || '';
                document.getElementById('channelName').value = workData.channel_name || '';
                document.getElementById('physicalProgress').value = workData.physical_progress || '';
                document.getElementById('workStatus').value = workData.work_status || '';
                document.getElementById('aaNoDate').value = workData.aa_no_date || '';
                document.getElementById('juniorEngineerWork').value = workData.junior_engineer || '';
                document.getElementById('workVillage').value = workData.village || '';
                document.getElementById('workConstituency').value = workData.constituency || '';
                document.getElementById('workDistrict').value = workData.district || '';
                document.getElementById('typeOfWork').value = workData.type_of_work || '';
                document.getElementById('totalLength').value = workData.total_length_to_be_lined || '';
                document.getElementById('lengthLined').value = workData.length_lined || '';
                document.getElementById('contractor').value = workData.contractor || '';
                document.getElementById('remarks').value = workData.remarks || '';

                // Update modal title
                document.getElementById('workModalTitle').textContent = 'Edit Work';

                // Show the modal
                const modal = new bootstrap.Modal(document.getElementById('workModal'));
                modal.show();

            } catch (error) {
                console.error('Error editing work:', error);
                alert('Error loading work data: ' + error.message);
            }
        };

        window.deleteWork = async function (workId) {
            try {
                console.log('Deleting work:', workId);

                // Get the work data - need to find which collection it's in
                let workSnapshot = null;
                let targetSubdivision = null;

                if (userRole === 'xen') {
                    // XEN can delete from any subdivision - need to find which one
                    const subdivisions = getAllSubdivisions();
                    for (const subdivision of subdivisions) {
                        try {
                            const collectionName = getWorkStatusCollectionName(subdivision);
                            const workDoc = doc(db, collectionName, workId);
                            const snapshot = await getDoc(workDoc);
                            if (snapshot.exists()) {
                                workSnapshot = snapshot;
                                targetSubdivision = subdivision;
                                break;
                            }
                        } catch (error) {
                            continue;
                        }
                    }
                } else {
                    // SDO can only delete from their subdivision
                    const collectionName = getWorkStatusCollectionName(userSubdivision);
                    const workDoc = doc(db, collectionName, workId);
                    workSnapshot = await getDoc(workDoc);
                    targetSubdivision = userSubdivision;
                }

                if (!workSnapshot || !workSnapshot.exists()) {
                    alert('Work record not found!');
                    return;
                }

                const workData = workSnapshot.data();

                if (confirm(`Are you sure you want to delete this work record?\n\nRD: ${workData.water_course_rd}\nChannel: ${workData.channel_name}\nContractor: ${workData.contractor}\n\nThis action cannot be undone!`)) {
                    const collectionName = getWorkStatusCollectionName(targetSubdivision);
                    await deleteDoc(doc(db, collectionName, workId));
                    alert('Work record deleted successfully!');
                    loadWorkStatus(); // Refresh the table
                }

            } catch (error) {
                console.error('Error deleting work:', error);
                alert('Error deleting work record: ' + error.message);
            }
        };

        // Date filtering
        window.filterByDate = function (type) {
            const days = type === 'applications' ?
                document.getElementById('dateFilterApps').value :
                document.getElementById('dateFilterWorks').value;

            if (!days) {
                // Show all data
                if (type === 'applications') {
                    loadNewApplications();
                } else {
                    loadWorkStatus();
                }
                return;
            }

            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - parseInt(days));

            // Filter table rows based on last updated date
            const tableId = type === 'applications' ? 'applicationsTableBody' : 'workStatusTableBody';
            const rows = document.querySelectorAll(`#${tableId} tr`);

            rows.forEach(row => {
                const lastUpdatedCell = row.cells[row.cells.length - (userRole === 'xen' ? 3 : 2)]; // Adjust for subdivision column
                const dateText = lastUpdatedCell.textContent;

                if (dateText) {
                    const rowDate = new Date(dateText);
                    if (rowDate >= cutoffDate) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                } else {
                    row.style.display = 'none';
                }
            });
        };

        // Refresh functions
        window.refreshApplications = function () {
            loadNewApplications();
        };

        window.refreshWorkStatus = function () {
            loadWorkStatus();
        };

        // Status filtering
        window.filterByStatus = function (type) {
            const statusValue = type === 'applications' ?
                document.getElementById('statusFilterApps').value :
                document.getElementById('statusFilterWorks').value;

            const tableId = type === 'applications' ? 'applicationsTableBody' : 'workStatusTableBody';
            const rows = document.querySelectorAll(`#${tableId} tr`);

            rows.forEach(row => {
                if (!statusValue) {
                    row.style.display = '';
                    return;
                }

                const statusCell = type === 'applications' ?
                    row.cells[6] : // Status column for applications
                    row.cells[3];   // Status column for works

                const statusText = statusCell.textContent.trim();

                if (statusText.includes(statusValue)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        };

        // Subdivision filtering (for XEN users)
        window.filterBySubdivision = function (type) {
            const subdivisionValue = type === 'applications' ?
                document.getElementById('subdivisionFilterApps').value :
                document.getElementById('subdivisionFilterWorks').value;

            const tableId = type === 'applications' ? 'applicationsTableBody' : 'workStatusTableBody';
            const rows = document.querySelectorAll(`#${tableId} tr`);

            rows.forEach(row => {
                if (!subdivisionValue) {
                    row.style.display = '';
                    return;
                }

                // Subdivision column is second to last (before Actions)
                const subdivisionCell = row.cells[row.cells.length - 2];
                const subdivisionText = subdivisionCell.textContent.trim();

                if (subdivisionText === subdivisionValue) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        };

        // Chart variables
        let applicationsChart = null;
        let worksChart = null;

        // Create pie chart for applications status
        function createApplicationsChart(data) {
            const ctx = document.getElementById('applicationsChart').getContext('2d');

            // Count status
            const statusCounts = {
                'In the norms': 0,
                'Out of norms': 0
            };

            data.forEach(app => {
                const status = app.status || 'Unknown';
                if (statusCounts.hasOwnProperty(status)) {
                    statusCounts[status]++;
                } else {
                    statusCounts['Unknown'] = (statusCounts['Unknown'] || 0) + 1;
                }
            });

            // Destroy existing chart
            if (applicationsChart) {
                applicationsChart.destroy();
            }

            applicationsChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: Object.keys(statusCounts),
                    datasets: [{
                        data: Object.values(statusCounts),
                        backgroundColor: [
                            '#28a745', // Green for "In the norms"
                            '#dc3545', // Red for "Out of norms"
                            '#6c757d'  // Gray for "Unknown"
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    aspectRatio: 1.5,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 10,
                                font: {
                                    size: 12
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function (context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed / total) * 100).toFixed(1);
                                    return `${context.label}: ${context.parsed} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });

            // Update stats
            const total = data.length;
            const inNorms = statusCounts['In the norms'] || 0;
            const outNorms = statusCounts['Out of norms'] || 0;

            document.getElementById('applicationsStats').innerHTML = `
                <div class="row text-center">
                    <div class="col-4">
                        <strong class="text-success">${inNorms}</strong><br>
                        <small>In Norms</small>
                    </div>
                    <div class="col-4">
                        <strong class="text-danger">${outNorms}</strong><br>
                        <small>Out Norms</small>
                    </div>
                    <div class="col-4">
                        <strong class="text-primary">${total}</strong><br>
                        <small>Total</small>
                    </div>
                </div>
            `;
        }

        // Create pie chart for work status
        function createWorksChart(data) {
            const ctx = document.getElementById('worksChart').getContext('2d');

            // Count status
            const statusCounts = {
                'Ongoing': 0,
                'Completed': 0,
                'Delayed': 0,
                'On Hold': 0
            };

            data.forEach(work => {
                const status = work.work_status || 'Unknown';
                if (statusCounts.hasOwnProperty(status)) {
                    statusCounts[status]++;
                } else {
                    statusCounts['Unknown'] = (statusCounts['Unknown'] || 0) + 1;
                }
            });

            // Destroy existing chart
            if (worksChart) {
                worksChart.destroy();
            }

            worksChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: Object.keys(statusCounts),
                    datasets: [{
                        data: Object.values(statusCounts),
                        backgroundColor: [
                            '#007bff', // Blue for "Ongoing"
                            '#28a745', // Green for "Completed"
                            '#dc3545', // Red for "Delayed"
                            '#ffc107', // Yellow for "On Hold"
                            '#6c757d'  // Gray for "Unknown"
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    aspectRatio: 1.5,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 10,
                                font: {
                                    size: 12
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function (context) {
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((context.parsed / total) * 100).toFixed(1);
                                    return `${context.label}: ${context.parsed} (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });

            // Update stats
            const total = data.length;
            const ongoing = statusCounts['Ongoing'] || 0;
            const completed = statusCounts['Completed'] || 0;
            const delayed = statusCounts['Delayed'] || 0;

            document.getElementById('worksStats').innerHTML = `
                <div class="row text-center">
                    <div class="col-3">
                        <strong class="text-primary">${ongoing}</strong><br>
                        <small>Ongoing</small>
                    </div>
                    <div class="col-3">
                        <strong class="text-success">${completed}</strong><br>
                        <small>Completed</small>
                    </div>
                    <div class="col-3">
                        <strong class="text-danger">${delayed}</strong><br>
                        <small>Delayed</small>
                    </div>
                    <div class="col-3">
                        <strong class="text-warning">${total}</strong><br>
                        <small>Total</small>
                    </div>
                </div>
            `;
        }

        // Update load functions to show date filters for XEN
        const originalLoadNewApplications = loadNewApplications;
        loadNewApplications = function () {
            originalLoadNewApplications();
            if (userRole === 'xen') {
                document.getElementById('xenDateFilter').style.display = 'block';
            }
        };

        const originalLoadWorkStatus = loadWorkStatus;
        loadWorkStatus = function () {
            originalLoadWorkStatus();
            if (userRole === 'xen') {
                document.getElementById('xenDateFilter2').style.display = 'block';
            }
        };

        // Open help page based on user role
        window.openHelp = function () {
            const helpPage = userRole === 'xen' ? 'help-xen.html' : 'help-sdo.html';
            window.open(helpPage, '_blank');
        };

        // Event listeners
        document.getElementById('signInBtn').addEventListener('click', signInWithGoogle);
        document.getElementById('signOutBtn').addEventListener('click', async () => {
            await signOut(auth);
        });

        // Auth state listener with persistence
        onAuthStateChanged(auth, (user) => {
            if (user) {
                console.log('👤 User signed in:', user.email);
                // Only check authorization if not already authorized
                if (!currentUser || currentUser.uid !== user.uid) {
                    checkUserAuthorization(user);
                }
            } else {
                console.log('👤 User signed out');
                // Clear current user data
                currentUser = null;
                userRole = null;
                userSubdivision = null;
                showLoginScreen();
            }
        });

        console.log('🔥 Simple Firebase app loaded successfully');
    </script>
</body>

</html>