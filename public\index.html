<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MICADA Division Monitoring System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="styles.css" rel="stylesheet">
</head>

<body>
    <!-- Login Modal -->
    <div class="modal fade" id="loginModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        MICADA Division Login
                    </h5>
                </div>
                <div class="modal-body">
                    <form id="loginForm">
                        <div class="mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" required>
                        </div>
                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" class="form-control" id="password" required>
                        </div>
                        <div id="loginError" class="alert alert-danger d-none"></div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-sign-in-alt me-2"></i>Login
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="mainApp" class="d-none">
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-water me-2"></i>
                    MICADA Division Monitoring
                </a>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text me-3" id="userInfo"></span>
                    <button class="btn btn-outline-light btn-sm" onclick="logout()">
                        <i class="fas fa-sign-out-alt me-1"></i>Logout
                    </button>
                </div>
            </div>
        </nav>

        <!-- Sidebar and Content -->
        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar -->
                <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
                    <div class="position-sticky pt-3">
                        <ul class="nav flex-column" id="sidebarNav">
                            <!-- Navigation items will be populated by JavaScript -->
                        </ul>
                    </div>
                </nav>

                <!-- Main Content -->
                <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                    <div
                        class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2" id="pageTitle">Dashboard</h1>
                    </div>

                    <!-- Dashboard Content -->
                    <div id="dashboardContent">
                        <div class="row">
                            <div class="col-md-6 mb-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">New Applications</h5>
                                                <h2 id="newAppsCount">0</h2>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-file-alt fa-3x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 mb-4">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h5 class="card-title">Active Works</h5>
                                                <h2 id="activeWorksCount">0</h2>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-tools fa-3x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- New Applications Content -->
                    <div id="newApplicationsContent" class="d-none">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3>New Water Course Applications</h3>
                            <div>
                                <button class="btn btn-success me-2" id="addApplicationBtn">
                                    <i class="fas fa-plus me-1"></i>Add Application
                                </button>
                                <button class="btn btn-info me-2" id="downloadApplicationTemplateBtn">
                                    <i class="fas fa-download me-1"></i>Download Template
                                </button>
                                <button class="btn btn-warning" id="uploadApplicationBtn">
                                    <i class="fas fa-upload me-1"></i>Bulk Upload
                                </button>
                            </div>
                        </div>

                        <!-- Search for Xen -->
                        <div id="xenSearchContainer" class="d-none mb-3">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="universalSearch"
                                    placeholder="Search across all fields (village, district, farmer name, etc.)">
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr id="applicationsTableHeader">
                                        <!-- Headers will be populated by JavaScript -->
                                    </tr>
                                </thead>
                                <tbody id="applicationsTableBody">
                                    <!-- Data will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Work Status Content -->
                    <div id="workStatusContent" class="d-none">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3>Work Status Monitoring</h3>
                            <div>
                                <button class="btn btn-success me-2" id="addWorkBtn">
                                    <i class="fas fa-plus me-1"></i>Add Work
                                </button>
                                <button class="btn btn-info me-2" id="downloadWorkTemplateBtn">
                                    <i class="fas fa-download me-1"></i>Download Template
                                </button>
                                <button class="btn btn-warning" id="uploadWorkBtn">
                                    <i class="fas fa-upload me-1"></i>Bulk Upload
                                </button>
                            </div>
                        </div>

                        <!-- Search for Xen -->
                        <div id="xenSearchContainer2" class="d-none mb-3">
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="universalSearch2"
                                    placeholder="Search across all fields (village, contractor, work name, etc.)">
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr id="workStatusTableHeader">
                                        <!-- Headers will be populated by JavaScript -->
                                    </tr>
                                </thead>
                                <tbody id="workStatusTableBody">
                                    <!-- Data will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- User Management Content (Xen only) -->
                    <div id="userManagementContent" class="d-none">
                        <h3>User Management</h3>
                        <p class="text-muted">Manage subdivision user credentials</p>

                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Subdivision</th>
                                        <th>Username</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTableBody">
                                    <!-- Users will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>

    </div>

    <!-- Add/Edit Application Modal -->
    <div class="modal fade" id="applicationModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="applicationModalTitle">Add New Application</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="applicationForm">
                        <input type="hidden" id="applicationId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="sr_no" class="form-label">Sr. No</label>
                                <input type="text" class="form-control" id="sr_no" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="village" class="form-label">Village</label>
                                <input type="text" class="form-control" id="village" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="constituency" class="form-label">Constituency</label>
                                <input type="text" class="form-control" id="constituency" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="district" class="form-label">District</label>
                                <input type="text" class="form-control" id="district" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="rd" class="form-label">RD</label>
                                <input type="text" class="form-control" id="rd" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="minor" class="form-label">Minor</label>
                                <input type="text" class="form-control" id="minor" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">Status (ATR)</label>
                                <input type="text" class="form-control" id="status" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="work_type" class="form-label">Work Type</label>
                                <select class="form-control" id="work_type" required>
                                    <option value="">Select Work Type</option>
                                    <option value="Extension">Extension</option>
                                    <option value="Remodelling">Remodelling</option>
                                    <option value="New Construction">New Construction</option>
                                    <option value="Repair">Repair</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="farmer_name" class="form-label">Farmer's Name</label>
                                <input type="text" class="form-control" id="farmer_name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="contact" class="form-label">Contact</label>
                                <input type="text" class="form-control" id="contact" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="reference" class="form-label">Reference</label>
                            <input type="text" class="form-control" id="reference">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveApplication()">Save Application</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add/Edit Work Modal -->
    <div class="modal fade" id="workModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="workModalTitle">Add New Work</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="workForm">
                        <input type="hidden" id="workId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="work_village" class="form-label">Village</label>
                                <input type="text" class="form-control" id="work_village" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="work_constituency" class="form-label">Constituency</label>
                                <input type="text" class="form-control" id="work_constituency" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="work_district" class="form-label">District</label>
                                <input type="text" class="form-control" id="work_district" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="water_course" class="form-label">Water Course Name</label>
                                <input type="text" class="form-control" id="water_course" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="work_name" class="form-label">Name of Work</label>
                            <input type="text" class="form-control" id="work_name" required>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="aa_no_date" class="form-label">AA No. & Date</label>
                                <input type="text" class="form-control" id="aa_no_date" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="type_of_work" class="form-label">Type of Work</label>
                                <select class="form-control" id="type_of_work" required>
                                    <option value="">Select Type</option>
                                    <option value="Remodelling">Remodelling</option>
                                    <option value="Lining">Lining</option>
                                    <option value="Extension">Extension</option>
                                    <option value="Repair">Repair</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="total_length_to_be_lined" class="form-label">Total Length to be
                                    Lined</label>
                                <input type="text" class="form-control" id="total_length_to_be_lined" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="length_lined" class="form-label">Length Lined</label>
                                <input type="text" class="form-control" id="length_lined" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="contractor" class="form-label">Contractor</label>
                                <input type="text" class="form-control" id="contractor" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="remarks" class="form-label">Remarks</label>
                                <textarea class="form-control" id="remarks" rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveWork()">Save Work</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit User Modal (Xen only) -->
    <div class="modal fade" id="userModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit User Credentials</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="userForm">
                        <input type="hidden" id="editUserId">
                        <div class="mb-3">
                            <label for="editSubdivision" class="form-label">Subdivision</label>
                            <input type="text" class="form-control" id="editSubdivision" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="editUsername" class="form-label">Username</label>
                            <input type="text" class="form-control" id="editUsername" required>
                        </div>
                        <div class="mb-3">
                            <label for="editPassword" class="form-label">New Password</label>
                            <input type="password" class="form-control" id="editPassword" required>
                        </div>
                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">Confirm Password</label>
                            <input type="password" class="form-control" id="confirmPassword" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveUser()">Update Credentials</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Upload Modal -->
    <div class="modal fade" id="uploadModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="uploadModalTitle">Bulk Upload</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="uploadFile" class="form-label">Select Excel File</label>
                        <input type="file" class="form-control" id="uploadFile" accept=".xlsx,.xls" required>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Please download the template first and fill it with your data before uploading.
                    </div>
                    <input type="hidden" id="uploadType">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="uploadFile()">Upload</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app.js"></script>
</body>

</html>