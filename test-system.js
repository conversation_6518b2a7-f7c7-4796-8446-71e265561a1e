// Simple test script for MICADA Division Monitoring System
const http = require('http');

function makeRequest(options, data = null) {
    return new Promise((resolve, reject) => {
        const req = http.request(options, (res) => {
            let body = '';
            res.on('data', (chunk) => {
                body += chunk;
            });
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    body: body
                });
            });
        });

        req.on('error', (err) => {
            reject(err);
        });

        if (data) {
            req.write(data);
        }
        req.end();
    });
}

async function testSystem() {
    console.log('🧪 Testing MICADA Division Monitoring System...\n');

    try {
        // Test 1: Check if server is running
        console.log('1. Testing server connectivity...');
        const homeResponse = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: '/',
            method: 'GET'
        });
        
        if (homeResponse.statusCode === 200) {
            console.log('✅ Server is running successfully');
        } else {
            console.log('❌ Server connectivity failed');
            return;
        }

        // Test 2: Test Xen login
        console.log('\n2. Testing Xen login...');
        const loginData = JSON.stringify({
            username: 'xenmicadahisar',
            password: 'dharmji@1947'
        });

        const loginResponse = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: '/api/login',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(loginData)
            }
        }, loginData);

        if (loginResponse.statusCode === 200) {
            const loginResult = JSON.parse(loginResponse.body);
            console.log('✅ Xen login successful');
            console.log(`   User: ${loginResult.user.name}`);
            console.log(`   Role: ${loginResult.user.role}`);
            
            // Test 3: Test API with authentication
            console.log('\n3. Testing authenticated API access...');
            const apiResponse = await makeRequest({
                hostname: 'localhost',
                port: 3000,
                path: '/api/new-applications',
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${loginResult.token}`
                }
            });

            if (apiResponse.statusCode === 200) {
                const applications = JSON.parse(apiResponse.body);
                console.log('✅ API access successful');
                console.log(`   Found ${applications.length} applications across all subdivisions`);
            } else {
                console.log('❌ API access failed');
            }

        } else {
            console.log('❌ Xen login failed');
            console.log('   Response:', loginResponse.body);
        }

        // Test 4: Test subdivision login
        console.log('\n4. Testing subdivision user login...');
        const subdivisionLoginData = JSON.stringify({
            username: 'narwana_user',
            password: 'narwana123'
        });

        const subdivisionLoginResponse = await makeRequest({
            hostname: 'localhost',
            port: 3000,
            path: '/api/login',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(subdivisionLoginData)
            }
        }, subdivisionLoginData);

        if (subdivisionLoginResponse.statusCode === 200) {
            const subdivisionResult = JSON.parse(subdivisionLoginResponse.body);
            console.log('✅ Subdivision login successful');
            console.log(`   User: ${subdivisionResult.user.name}`);
            console.log(`   Role: ${subdivisionResult.user.role}`);
        } else {
            console.log('❌ Subdivision login failed');
        }

        console.log('\n🎉 System testing completed!');
        console.log('\n📋 Summary:');
        console.log('   • Server is running on http://localhost:3000');
        console.log('   • Xen login: xenmicadahisar / dharmji@1947');
        console.log('   • Subdivision users can login with their credentials');
        console.log('   • API endpoints are working correctly');
        console.log('   • Sample data is loaded and accessible');
        
        console.log('\n🚀 Ready for use! Open http://localhost:3000 in your browser.');

    } catch (error) {
        console.error('❌ Test failed:', error.message);
        console.log('\n💡 Make sure the server is running with: npm start');
    }
}

testSystem();
