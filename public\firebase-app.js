// Firebase Application Logic
import { collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

// Wait for Firebase to be initialized
setTimeout(() => {
    initializeApp();
}, 1000);

function initializeApp() {
    console.log('Firebase app initialized');

    // Make functions globally accessible
    window.showSection = showSection;
    window.showAddApplicationModal = showAddApplicationModal;
    window.showAddWorkModal = showAddWorkModal;
    window.downloadTemplate = downloadTemplate;
    window.showUploadModal = showUploadModal;
    window.saveApplication = saveApplication;
    window.saveWork = saveWork;
    window.editApplication = editApplication;
    window.editWork = editWork;
    window.deleteApplication = deleteApplication;
    window.deleteWork = deleteWork;
    window.handleUniversalSearch = handleUniversalSearch;
    window.handleUniversalSearchWorks = handleUniversalSearchWorks;
}

// Show specific section
function showSection(section) {
    console.log('showSection called with:', section);

    try {
        // Hide all content sections
        const sections = ['dashboardContent', 'newApplicationsContent', 'workStatusContent', 'userManagementContent'];
        sections.forEach(s => {
            const element = document.getElementById(s);
            if (element) {
                element.classList.add('d-none');
            }
        });

        // Remove active class from all nav items
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });

        // Show selected section and set nav active
        const navElement = document.getElementById('nav-' + section);
        if (navElement) {
            navElement.classList.add('active');
        }

        switch (section) {
            case 'dashboard':
                document.getElementById('dashboardContent').classList.remove('d-none');
                document.getElementById('pageTitle').textContent = 'Dashboard';
                loadDashboard();
                break;
            case 'newApplications':
                document.getElementById('newApplicationsContent').classList.remove('d-none');
                document.getElementById('pageTitle').textContent = 'New Water Course Applications';
                loadNewApplications();
                break;
            case 'workStatus':
                document.getElementById('workStatusContent').classList.remove('d-none');
                document.getElementById('pageTitle').textContent = 'Work Status Monitoring';
                loadWorkStatus();
                break;
            case 'userManagement':
                if (window.userRole === 'xen') {
                    document.getElementById('userManagementContent').classList.remove('d-none');
                    document.getElementById('pageTitle').textContent = 'User Management';
                    loadUserManagement();
                }
                break;
        }

        console.log('showSection completed for:', section);

    } catch (error) {
        console.error('Error in showSection:', error);
        alert('Error switching sections: ' + error.message);
    }
}

// Load dashboard data
async function loadDashboard() {
    try {
        const applicationsQuery = window.userRole === 'xen'
            ? query(collection(window.db, 'new_applications'))
            : query(collection(window.db, 'new_applications'), where('subdivision', '==', window.userSubdivision));

        const worksQuery = window.userRole === 'xen'
            ? query(collection(window.db, 'work_status'))
            : query(collection(window.db, 'work_status'), where('subdivision', '==', window.userSubdivision));

        const [applicationsSnapshot, worksSnapshot] = await Promise.all([
            getDocs(applicationsQuery),
            getDocs(worksQuery)
        ]);

        document.getElementById('newAppsCount').textContent = applicationsSnapshot.size;
        document.getElementById('activeWorksCount').textContent = worksSnapshot.size;

    } catch (error) {
        console.error('Error loading dashboard:', error);
    }
}

// Load new applications
async function loadNewApplications() {
    console.log('Loading new applications from Firebase...');
    try {
        const applicationsQuery = window.userRole === 'xen'
            ? query(collection(window.db, 'new_applications'), orderBy('createdAt', 'desc'))
            : query(collection(window.db, 'new_applications'),
                where('subdivision', '==', window.userSubdivision),
                orderBy('createdAt', 'desc'));

        const querySnapshot = await getDocs(applicationsQuery);
        const applications = [];

        querySnapshot.forEach((doc) => {
            applications.push({ id: doc.id, ...doc.data() });
        });

        console.log('Applications loaded:', applications.length, 'items');
        displayApplicationsTable(applications);

        // Show search for Xen users
        if (window.userRole === 'xen') {
            document.getElementById('xenSearchContainer').classList.remove('d-none');
        }

    } catch (error) {
        console.error('Error loading applications:', error);
        alert('Error loading applications: ' + error.message);
    }
}

// Load work status
async function loadWorkStatus() {
    console.log('Loading work status from Firebase...');
    try {
        const worksQuery = window.userRole === 'xen'
            ? query(collection(window.db, 'work_status'), orderBy('createdAt', 'desc'))
            : query(collection(window.db, 'work_status'),
                where('subdivision', '==', window.userSubdivision),
                orderBy('createdAt', 'desc'));

        const querySnapshot = await getDocs(worksQuery);
        const works = [];

        querySnapshot.forEach((doc) => {
            works.push({ id: doc.id, ...doc.data() });
        });

        console.log('Works loaded:', works.length, 'items');
        displayWorkStatusTable(works);

        // Show search for Xen users
        if (window.userRole === 'xen') {
            document.getElementById('xenSearchContainer2').classList.remove('d-none');
        }

    } catch (error) {
        console.error('Error loading work status:', error);
        alert('Error loading work status: ' + error.message);
    }
}

// Load user management
async function loadUserManagement() {
    if (window.userRole !== 'xen') return;

    try {
        const querySnapshot = await getDocs(collection(window.db, 'users'));
        const users = [];

        querySnapshot.forEach((doc) => {
            users.push({ id: doc.id, ...doc.data() });
        });

        displayUsersTable(users);

    } catch (error) {
        console.error('Error loading users:', error);
    }
}

// Display applications table
function displayApplicationsTable(applications) {
    const headerRow = document.getElementById('applicationsTableHeader');
    const tbody = document.getElementById('applicationsTableBody');

    // Define headers
    let headers = ['Sr. No', 'Village', 'Constituency', 'District', 'RD', 'Minor', 'Status', 'Work Type', 'Farmer Name', 'Contact', 'Reference'];

    if (window.userRole === 'xen') {
        headers.push('Subdivision');
    }

    if (window.userRole === 'subdivision') {
        headers.push('Actions');
    }

    // Build header
    headerRow.innerHTML = headers.map(header => `<th>${header}</th>`).join('');

    // Build table body
    tbody.innerHTML = applications.map(app => {
        let row = `
            <tr>
                <td>${app.sr_no || ''}</td>
                <td>${app.village || ''}</td>
                <td>${app.constituency || ''}</td>
                <td>${app.district || ''}</td>
                <td>${app.rd || ''}</td>
                <td>${app.minor || ''}</td>
                <td><span class="badge status-${getStatusClass(app.status)}">${app.status || ''}</span></td>
                <td>${app.work_type || ''}</td>
                <td>${app.farmer_name || ''}</td>
                <td>${app.contact || ''}</td>
                <td>${app.reference || ''}</td>
        `;

        if (window.userRole === 'xen') {
            row += `<td>${app.subdivision || ''}</td>`;
        }

        if (window.userRole === 'subdivision') {
            row += `
                <td>
                    <button class="btn btn-sm btn-primary me-1" onclick="editApplication('${app.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteApplication('${app.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
        }

        row += '</tr>';
        return row;
    }).join('');
}

// Display work status table
function displayWorkStatusTable(works) {
    const headerRow = document.getElementById('workStatusTableHeader');
    const tbody = document.getElementById('workStatusTableBody');

    // Define headers
    let headers = ['Village', 'Constituency', 'District', 'Water Course', 'Work Name', 'AA No. & Date', 'Type of Work', 'Total Length', 'Length Lined', 'Contractor', 'Remarks'];

    if (window.userRole === 'xen') {
        headers.push('Subdivision');
    }

    if (window.userRole === 'subdivision') {
        headers.push('Actions');
    }

    // Build header
    headerRow.innerHTML = headers.map(header => `<th>${header}</th>`).join('');

    // Build table body
    tbody.innerHTML = works.map(work => {
        let row = `
            <tr>
                <td>${work.village || ''}</td>
                <td>${work.constituency || ''}</td>
                <td>${work.district || ''}</td>
                <td>${work.water_course || ''}</td>
                <td>${work.work_name || ''}</td>
                <td>${work.aa_no_date || ''}</td>
                <td>${work.type_of_work || ''}</td>
                <td>${work.total_length_to_be_lined || ''}</td>
                <td>${work.length_lined || ''}</td>
                <td>${work.contractor || ''}</td>
                <td>${work.remarks || ''}</td>
        `;

        if (window.userRole === 'xen') {
            row += `<td>${work.subdivision || ''}</td>`;
        }

        if (window.userRole === 'subdivision') {
            row += `
                <td>
                    <button class="btn btn-sm btn-primary me-1" onclick="editWork('${work.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteWork('${work.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
        }

        row += '</tr>';
        return row;
    }).join('');
}

// Display users table
function displayUsersTable(users) {
    const tbody = document.getElementById('usersTableBody');

    tbody.innerHTML = users.map(user => `
        <tr>
            <td>${user.subdivision || 'XEN Office'}</td>
            <td>${user.email}</td>
            <td>${user.lastLogin ? new Date(user.lastLogin.seconds * 1000).toLocaleDateString() : 'Never'}</td>
            <td><span class="badge bg-success">Active</span></td>
        </tr>
    `).join('');
}

// Get status CSS class
function getStatusClass(status) {
    if (!status) return 'pending';
    const statusLower = status.toLowerCase();
    if (statusLower.includes('approved')) return 'approved';
    if (statusLower.includes('rejected')) return 'rejected';
    if (statusLower.includes('progress')) return 'in-progress';
    return 'pending';
}

// Modal functions
function showAddApplicationModal() {
    alert('Add Application Modal - Firebase version');
}

function showAddWorkModal() {
    alert('Add Work Modal - Firebase version');
}

function downloadTemplate(type) {
    console.log('downloadTemplate called with type:', type);
    let headers, filename;

    if (type === 'applications') {
        headers = ['Sr. No', 'Village', 'Constituency', 'District', 'RD', 'MINOR', 'Status', 'Work Type', "Farmer's Name", 'Contact', 'Reference'];
        filename = 'new_applications_template.csv';
    } else if (type === 'works') {
        headers = ['Village', 'Constituency', 'District', 'Water Course Name', 'Name of Work', 'AA No. & Date', 'Type of Work', 'Total Length to be Lined', 'Length Lined', 'Contractor', 'Remarks'];
        filename = 'work_status_template.csv';
    }

    const csvContent = headers.join(',') + '\n';
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

function showUploadModal(type) {
    alert('Upload Modal - Firebase version');
}

// CRUD operations
async function saveApplication() {
    alert('Save Application - Firebase version');
}

async function saveWork() {
    alert('Save Work - Firebase version');
}

async function editApplication(id) {
    alert('Edit Application: ' + id);
}

async function editWork(id) {
    alert('Edit Work: ' + id);
}

async function deleteApplication(id) {
    if (confirm('Are you sure you want to delete this application?')) {
        try {
            await deleteDoc(doc(window.db, 'new_applications', id));
            alert('Application deleted successfully');
            loadNewApplications(); // Reload the table
        } catch (error) {
            console.error('Error deleting application:', error);
            alert('Error deleting application');
        }
    }
}

async function deleteWork(id) {
    if (confirm('Are you sure you want to delete this work?')) {
        try {
            await deleteDoc(doc(window.db, 'work_status', id));
            alert('Work deleted successfully');
            loadWorkStatus(); // Reload the table
        } catch (error) {
            console.error('Error deleting work:', error);
            alert('Error deleting work');
        }
    }
}

// Search functions
function handleUniversalSearch(e) {
    const searchTerm = e.target.value.toLowerCase();
    const tableRows = document.querySelectorAll('#applicationsTableBody tr');

    tableRows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function handleUniversalSearchWorks(e) {
    const searchTerm = e.target.value.toLowerCase();
    const tableRows = document.querySelectorAll('#workStatusTableBody tr');

    tableRows.forEach(row => {
        const text = row.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}
