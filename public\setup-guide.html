<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Firebase Setup Guide - MICADA Division</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h2 class="mb-0">
                            <i class="fas fa-rocket me-2"></i>
                            Complete Firebase Setup Guide for MICADA Division
                        </h2>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h5><i class="fas fa-exclamation-triangle me-2"></i>Important</h5>
                            <p class="mb-0">Follow these steps exactly to set up your Firebase project correctly.</p>
                        </div>

                        <!-- Step 1 -->
                        <div class="card mb-4">
                            <div class="card-header bg-success text-white">
                                <h4 class="mb-0">Step 1: Create Firebase Project</h4>
                            </div>
                            <div class="card-body">
                                <ol>
                                    <li><strong>Go to:</strong> <a href="https://console.firebase.google.com/" target="_blank">https://console.firebase.google.com/</a></li>
                                    <li><strong>Click:</strong> "Create a project"</li>
                                    <li><strong>Project name:</strong> <code>MICADA Division System</code></li>
                                    <li><strong>Project ID:</strong> Will be auto-generated (like <code>micada-division-system-12345</code>)</li>
                                    <li><strong>Google Analytics:</strong> Choose "No" for now</li>
                                    <li><strong>Click:</strong> "Create project"</li>
                                    <li><strong>Wait:</strong> For project creation to complete</li>
                                </ol>
                            </div>
                        </div>

                        <!-- Step 2 -->
                        <div class="card mb-4">
                            <div class="card-header bg-info text-white">
                                <h4 class="mb-0">Step 2: Enable Authentication</h4>
                            </div>
                            <div class="card-body">
                                <ol>
                                    <li><strong>In Firebase Console:</strong> Click "Authentication" in left sidebar</li>
                                    <li><strong>Click:</strong> "Get started"</li>
                                    <li><strong>Go to:</strong> "Sign-in method" tab</li>
                                    <li><strong>Click on:</strong> "Google" provider</li>
                                    <li><strong>Toggle:</strong> "Enable"</li>
                                    <li><strong>Project support email:</strong> Select your email</li>
                                    <li><strong>Click:</strong> "Save"</li>
                                </ol>
                            </div>
                        </div>

                        <!-- Step 3 -->
                        <div class="card mb-4">
                            <div class="card-header bg-warning text-dark">
                                <h4 class="mb-0">Step 3: Create Firestore Database</h4>
                            </div>
                            <div class="card-body">
                                <ol>
                                    <li><strong>Click:</strong> "Firestore Database" in left sidebar</li>
                                    <li><strong>Click:</strong> "Create database"</li>
                                    <li><strong>Security rules:</strong> Choose "Start in test mode"</li>
                                    <li><strong>Location:</strong> Select "asia-south1 (Mumbai)" - closest to India</li>
                                    <li><strong>Click:</strong> "Done"</li>
                                    <li><strong>Wait:</strong> For database creation to complete</li>
                                </ol>
                            </div>
                        </div>

                        <!-- Step 4 -->
                        <div class="card mb-4">
                            <div class="card-header bg-danger text-white">
                                <h4 class="mb-0">Step 4: Get Firebase Configuration</h4>
                            </div>
                            <div class="card-body">
                                <ol>
                                    <li><strong>Go to:</strong> Project Settings (gear icon)</li>
                                    <li><strong>Scroll down to:</strong> "Your apps" section</li>
                                    <li><strong>Click:</strong> Web app icon (<code>&lt;/&gt;</code>)</li>
                                    <li><strong>App nickname:</strong> <code>MICADA Web App</code></li>
                                    <li><strong>Firebase Hosting:</strong> Leave unchecked</li>
                                    <li><strong>Click:</strong> "Register app"</li>
                                    <li><strong>Copy:</strong> The entire firebaseConfig object</li>
                                </ol>
                                
                                <div class="alert alert-info mt-3">
                                    <h6>It will look like this:</h6>
                                    <pre class="mb-0"><code>const firebaseConfig = {
  apiKey: "AIzaSyC4bwhHbZ9oc6mPkzIbQKJyIk9sSiA-S9s",
  authDomain: "your-project-id.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project-id.appspot.com",
  messagingSenderId: "363841115848",
  appId: "1:363841115848:web:68ce295c4375e68fe077fd"
};</code></pre>
                                </div>
                            </div>
                        </div>

                        <!-- Step 5 -->
                        <div class="card mb-4">
                            <div class="card-header bg-secondary text-white">
                                <h4 class="mb-0">Step 5: Update Configuration File</h4>
                            </div>
                            <div class="card-body">
                                <ol>
                                    <li><strong>Open:</strong> <code>firebase-config-template.js</code> file</li>
                                    <li><strong>Replace:</strong> The placeholder config with your actual config</li>
                                    <li><strong>Save as:</strong> <code>firebase-config.js</code></li>
                                    <li><strong>Update:</strong> Authorized user emails if needed</li>
                                </ol>
                                
                                <div class="alert alert-warning mt-3">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Important:</h6>
                                    <p class="mb-0">Make sure to replace ALL the placeholder values with your actual Firebase config values.</p>
                                </div>
                            </div>
                        </div>

                        <!-- Step 6 -->
                        <div class="card mb-4">
                            <div class="card-header bg-dark text-white">
                                <h4 class="mb-0">Step 6: Configure Authorized Domains</h4>
                            </div>
                            <div class="card-body">
                                <h6>In Firebase Console:</h6>
                                <ol>
                                    <li><strong>Go to:</strong> Authentication → Settings → Authorized domains</li>
                                    <li><strong>Add:</strong> <code>localhost</code> (should already be there)</li>
                                    <li><strong>Add:</strong> Your domain if you plan to deploy</li>
                                </ol>
                                
                                <h6 class="mt-3">In Google Cloud Console:</h6>
                                <ol>
                                    <li><strong>Go to:</strong> <a href="https://console.cloud.google.com/" target="_blank">Google Cloud Console</a></li>
                                    <li><strong>Select:</strong> Your Firebase project</li>
                                    <li><strong>Go to:</strong> APIs & Services → Credentials</li>
                                    <li><strong>Find:</strong> Web client (auto created by Firebase)</li>
                                    <li><strong>Add Authorized JavaScript origins:</strong> <code>http://localhost:3000</code></li>
                                    <li><strong>Save</strong></li>
                                </ol>
                            </div>
                        </div>

                        <!-- Step 7 -->
                        <div class="card mb-4">
                            <div class="card-header bg-success text-white">
                                <h4 class="mb-0">Step 7: Initialize Database</h4>
                            </div>
                            <div class="card-body">
                                <ol>
                                    <li><strong>Open:</strong> <a href="setup.html" target="_blank">setup.html</a></li>
                                    <li><strong>Click:</strong> "Start Firebase Setup"</li>
                                    <li><strong>Wait:</strong> For setup to complete</li>
                                    <li><strong>Verify:</strong> Collections are created in Firestore</li>
                                </ol>
                            </div>
                        </div>

                        <!-- Step 8 -->
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h4 class="mb-0">Step 8: Test the Application</h4>
                            </div>
                            <div class="card-body">
                                <ol>
                                    <li><strong>Open:</strong> <a href="index-firebase.html" target="_blank">index-firebase.html</a></li>
                                    <li><strong>Click:</strong> "Sign in with Google"</li>
                                    <li><strong>Use:</strong> <code><EMAIL></code> or authorized email</li>
                                    <li><strong>Test:</strong> Navigation and functionality</li>
                                </ol>
                            </div>
                        </div>

                        <!-- Troubleshooting -->
                        <div class="card">
                            <div class="card-header bg-warning text-dark">
                                <h4 class="mb-0">
                                    <i class="fas fa-tools me-2"></i>
                                    Troubleshooting
                                </h4>
                            </div>
                            <div class="card-body">
                                <h6>Common Issues:</h6>
                                <ul>
                                    <li><strong>Auth domain error:</strong> Check authorized domains in Firebase Console</li>
                                    <li><strong>Permission denied:</strong> Make sure Firestore is in test mode</li>
                                    <li><strong>Config error:</strong> Verify all config values are correct</li>
                                    <li><strong>Sign-in popup blocked:</strong> Allow popups for localhost</li>
                                </ul>
                                
                                <h6 class="mt-3">Check Browser Console:</h6>
                                <p>Press F12 and check Console tab for detailed error messages.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
