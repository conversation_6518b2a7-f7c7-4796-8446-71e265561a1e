<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Button Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Simple Button Test</h2>
        <p>Testing if buttons work with clean JavaScript</p>
        
        <div class="mb-3">
            <button class="btn btn-success me-2" onclick="window.showAddApplicationModal()">
                <i class="fas fa-plus me-1"></i>Add Application (Test)
            </button>
            
            <button class="btn btn-info me-2" onclick="window.downloadTemplate('applications')">
                <i class="fas fa-download me-1"></i>Download Template (Test)
            </button>
            
            <button class="btn btn-warning" onclick="alert('Direct onclick test works!')">
                <i class="fas fa-test me-1"></i>Direct Test
            </button>
        </div>
        
        <div class="alert alert-info">
            <h5>Console Output:</h5>
            <div id="output" style="font-family: monospace; background: #f8f9fa; padding: 10px; height: 200px; overflow-y: auto;">
                Waiting for button clicks...<br>
            </div>
        </div>
    </div>

    <!-- Test Modal -->
    <div class="modal fade" id="applicationModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="applicationModalTitle">Test Application Modal</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="applicationForm">
                        <input type="hidden" id="applicationId">
                        <div class="mb-3">
                            <label for="test_field" class="form-label">Test Field</label>
                            <input type="text" class="form-control" id="test_field" placeholder="This is a test modal">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary">Test Save</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app-clean.js"></script>
    <script>
        // Override console.log to show in page
        const originalLog = console.log;
        const output = document.getElementById('output');
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${args.join(' ')}<br>`;
            output.scrollTop = output.scrollHeight;
        };
        
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Simple test page loaded');
            console.log('Bootstrap available:', typeof bootstrap !== 'undefined');
            console.log('showAddApplicationModal available:', typeof window.showAddApplicationModal);
            console.log('downloadTemplate available:', typeof window.downloadTemplate);
        });
    </script>
</body>
</html>
