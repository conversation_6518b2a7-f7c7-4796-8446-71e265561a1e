<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Migrate to Subdivision Collections - MICADA</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3>Migrate to Subdivision-Specific Collections</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle me-2"></i>New Collection Structure</h5>
                            <p>This migration will create separate collections for each subdivision:</p>
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Applications:</h6>
                                    <ul>
                                        <li><code>applications_narwana</code></li>
                                        <li><code>applications_barwala</code></li>
                                        <li><code>applications_hisar1</code></li>
                                        <li><code>applications_hisar2</code></li>
                                    </ul>
                                </div>
                                <div class="col-md-6">
                                    <h6>Work Status:</h6>
                                    <ul>
                                        <li><code>work_status_narwana</code></li>
                                        <li><code>work_status_barwala</code></li>
                                        <li><code>work_status_hisar1</code></li>
                                        <li><code>work_status_hisar2</code></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="confirmMigration">
                                <label class="form-check-label" for="confirmMigration">
                                    I understand this will reorganize the database structure
                                </label>
                            </div>
                        </div>
                        
                        <button id="migrateBtn" class="btn btn-primary btn-lg" disabled>Start Migration</button>
                        <button id="testBtn" class="btn btn-info">Test Connection</button>
                        
                        <div id="migrationProgress" class="mt-4 d-none">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="mt-2">
                                <small id="progressText" class="text-muted">Starting migration...</small>
                            </div>
                        </div>

                        <div id="migrationResults" class="mt-4 d-none">
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle me-2"></i>Migration Completed!</h5>
                                <p class="mb-0">Data has been successfully migrated to subdivision-specific collections.</p>
                            </div>
                        </div>

                        <div id="migrationOutput" class="mt-4 p-3 bg-light" style="height: 400px; overflow-y: auto; font-family: monospace; font-size: 12px;">
                            Migration output will appear here...<br>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, getDocs, doc, addDoc, deleteDoc } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyC4bwhHbZ9oc6mPkzIbQKJyIk9sSiA-S9s",
            authDomain: "micada-division.firebaseapp.com",
            projectId: "micada-division",
            storageBucket: "micada-division.appspot.com",
            messagingSenderId: "363841115848",
            appId: "1:363841115848:web:68ce295c4375e68fe077fd",
            measurementId: "G-1RXZT0K512"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        function log(message) {
            const output = document.getElementById('migrationOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}<br>`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        function updateProgress(percentage, text) {
            const progressBar = document.querySelector('.progress-bar');
            const progressText = document.getElementById('progressText');
            progressBar.style.width = percentage + '%';
            progressText.textContent = text;
        }

        // Helper functions
        function getApplicationsCollectionName(subdivision) {
            return `applications_${subdivision}`;
        }

        function getWorkStatusCollectionName(subdivision) {
            return `work_status_${subdivision}`;
        }

        function getAllSubdivisions() {
            return ['narwana', 'barwala', 'hisar1', 'hisar2'];
        }

        // Enable migration button when checkbox is checked
        document.getElementById('confirmMigration').addEventListener('change', function() {
            document.getElementById('migrateBtn').disabled = !this.checked;
        });

        // Test connection
        document.getElementById('testBtn').addEventListener('click', async () => {
            try {
                log('🔍 Testing database connection...');
                
                const appsSnapshot = await getDocs(collection(db, 'new_applications'));
                log(`✅ Old applications collection: ${appsSnapshot.size} documents`);
                
                const worksSnapshot = await getDocs(collection(db, 'work_status'));
                log(`✅ Old work status collection: ${worksSnapshot.size} documents`);
                
                log('✅ Database connection successful!');
                
            } catch (error) {
                log('❌ Database connection failed: ' + error.message);
            }
        });

        // Start migration
        document.getElementById('migrateBtn').addEventListener('click', async () => {
            const migrateBtn = document.getElementById('migrateBtn');
            const migrationProgress = document.getElementById('migrationProgress');
            const migrationResults = document.getElementById('migrationResults');
            
            migrateBtn.disabled = true;
            migrationProgress.classList.remove('d-none');
            migrationResults.classList.add('d-none');
            
            try {
                log('🚀 Starting subdivision collection migration...');
                updateProgress(10, 'Connecting to database...');
                
                // Step 1: Migrate Applications
                log('📄 Migrating applications to subdivision collections...');
                updateProgress(30, 'Migrating applications...');
                await migrateApplications();
                
                // Step 2: Migrate Work Status
                log('🔧 Migrating work status to subdivision collections...');
                updateProgress(60, 'Migrating work status...');
                await migrateWorkStatus();
                
                // Step 3: Create sample data for empty subdivisions
                log('📊 Creating sample data for subdivisions...');
                updateProgress(80, 'Creating sample data...');
                await createSampleData();
                
                updateProgress(100, 'Migration completed successfully!');
                
                setTimeout(() => {
                    migrationProgress.classList.add('d-none');
                    migrationResults.classList.remove('d-none');
                }, 1000);
                
                log('✅ Migration completed successfully!');
                
            } catch (error) {
                log('❌ Migration failed: ' + error.message);
                migrateBtn.disabled = false;
            }
        });

        // Migrate applications
        async function migrateApplications() {
            try {
                const querySnapshot = await getDocs(collection(db, 'new_applications'));
                let count = 0;
                
                for (const docSnapshot of querySnapshot.docs) {
                    const data = docSnapshot.data();
                    const subdivision = data.subdivision || 'narwana'; // Default to narwana if not specified
                    
                    // Add work_status field if missing
                    if (!data.work_status) {
                        data.work_status = 'Ongoing'; // Default status
                    }
                    
                    const newCollectionName = getApplicationsCollectionName(subdivision);
                    
                    // Add to new subdivision-specific collection
                    await addDoc(collection(db, newCollectionName), data);
                    
                    count++;
                    log(`  ✓ Migrated application ${docSnapshot.id} to ${newCollectionName}`);
                }
                
                log(`✅ Migrated ${count} applications to subdivision collections`);
                
            } catch (error) {
                log(`❌ Error migrating applications: ${error.message}`);
                throw error;
            }
        }

        // Migrate work status
        async function migrateWorkStatus() {
            try {
                const querySnapshot = await getDocs(collection(db, 'work_status'));
                let count = 0;
                
                for (const docSnapshot of querySnapshot.docs) {
                    const data = docSnapshot.data();
                    const subdivision = data.subdivision || 'narwana'; // Default to narwana if not specified
                    
                    const newCollectionName = getWorkStatusCollectionName(subdivision);
                    
                    // Add to new subdivision-specific collection
                    await addDoc(collection(db, newCollectionName), data);
                    
                    count++;
                    log(`  ✓ Migrated work status ${docSnapshot.id} to ${newCollectionName}`);
                }
                
                log(`✅ Migrated ${count} work status records to subdivision collections`);
                
            } catch (error) {
                log(`❌ Error migrating work status: ${error.message}`);
                throw error;
            }
        }

        // Create sample data for subdivisions
        async function createSampleData() {
            const subdivisions = getAllSubdivisions();
            
            for (const subdivision of subdivisions) {
                try {
                    // Check if subdivision already has data
                    const appsSnapshot = await getDocs(collection(db, getApplicationsCollectionName(subdivision)));
                    const worksSnapshot = await getDocs(collection(db, getWorkStatusCollectionName(subdivision)));
                    
                    if (appsSnapshot.size === 0) {
                        // Create sample application
                        const sampleApp = {
                            sr_no: '1',
                            village: `Sample Village ${subdivision}`,
                            constituency: subdivision === 'narwana' ? 'Kaithal' : 'Hisar',
                            district: subdivision === 'narwana' ? 'Kaithal' : 'Hisar',
                            rd: '1500/L',
                            minor: `${subdivision} minor`,
                            status: 'In the norms',
                            reason_out_of_norms: '',
                            work_type: 'Canal Lining',
                            farmer_name: 'Sample Farmer',
                            contact: '9876543210',
                            reference: `REF-${subdivision}-001/2024`,
                            junior_engineer: `Er. ${subdivision} Engineer`,
                            createdAt: new Date(),
                            createdBy: 'migration',
                            lastUpdated: new Date()
                        };
                        
                        await addDoc(collection(db, getApplicationsCollectionName(subdivision)), sampleApp);
                        log(`  ✓ Created sample application for ${subdivision}`);
                    }
                    
                    if (worksSnapshot.size === 0) {
                        // Create sample work
                        const sampleWork = {
                            water_course_rd: '1500/L',
                            channel_name: `${subdivision} distributary`,
                            physical_progress: 50,
                            work_status: 'Ongoing',
                            aa_no_date: `AA-${subdivision}-001/2024 dt. 01.01.2024`,
                            type_of_work: 'Canal Lining',
                            total_length_to_be_lined: '1000 RFT',
                            length_lined: '500 RFT',
                            contractor: `M/s ${subdivision} Construction`,
                            junior_engineer: `Er. ${subdivision} Engineer`,
                            remarks: 'Sample work record',
                            village: `Sample Village ${subdivision}`,
                            constituency: subdivision === 'narwana' ? 'Kaithal' : 'Hisar',
                            district: subdivision === 'narwana' ? 'Kaithal' : 'Hisar',
                            createdAt: new Date(),
                            createdBy: 'migration',
                            lastUpdated: new Date()
                        };
                        
                        await addDoc(collection(db, getWorkStatusCollectionName(subdivision)), sampleWork);
                        log(`  ✓ Created sample work for ${subdivision}`);
                    }
                    
                } catch (error) {
                    log(`❌ Error creating sample data for ${subdivision}: ${error.message}`);
                }
            }
            
            log('✅ Sample data creation completed');
        }

        log('🔥 Subdivision collection migration script loaded');
        log('Click "Test Connection" to verify database access');
        log('Check the confirmation box and click "Start Migration" to proceed');
    </script>
</body>
</html>
