// MICADA Division Monitoring System - Minimal Working Version

// Global variables
let currentUser = null;
let authToken = null;
let currentSection = 'dashboard';

// Essential functions only
function showAddApplicationModal() {
    console.log('showAddApplicationModal called');
    try {
        const modalElement = document.getElementById('applicationModal');
        if (!modalElement) {
            alert('Modal not found. Please refresh the page.');
            return;
        }

        document.getElementById('applicationModalTitle').textContent = 'Add New Application';
        document.getElementById('applicationForm').reset();
        document.getElementById('applicationId').value = '';

        const modal = new bootstrap.Modal(modalElement);
        modal.show();
        console.log('Modal shown successfully');
    } catch (error) {
        console.error('Error showing modal:', error);
        alert('Error opening form: ' + error.message);
    }
}

function showAddWorkModal() {
    console.log('showAddWorkModal called');
    try {
        document.getElementById('workModalTitle').textContent = 'Add New Work';
        document.getElementById('workForm').reset();
        document.getElementById('workId').value = '';

        const modal = new bootstrap.Modal(document.getElementById('workModal'));
        modal.show();
        console.log('Work modal shown');
    } catch (error) {
        console.error('Error showing work modal:', error);
        alert('Error opening form: ' + error.message);
    }
}

function downloadTemplate(type) {
    console.log('downloadTemplate called with type:', type);
    let headers, filename;

    if (type === 'applications') {
        headers = ['Sr. No', 'Village', 'Constituency', 'District', 'RD', 'MINOR', 'Status', 'Work Type', "Farmer's Name", 'Contact', 'Reference'];
        filename = 'new_applications_template.csv';
    } else if (type === 'works') {
        headers = ['Village', 'Constituency', 'District', 'Water Course Name', 'Name of Work', 'AA No. & Date', 'Type of Work', 'Total Length to be Lined', 'Length Lined', 'Contractor', 'Remarks'];
        filename = 'work_status_template.csv';
    }

    const csvContent = headers.join(',') + '\n';
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

function showUploadModal(type) {
    console.log('showUploadModal called with type:', type);
    document.getElementById('uploadType').value = type;
    document.getElementById('uploadModalTitle').textContent =
        type === 'applications' ? 'Bulk Upload Applications' : 'Bulk Upload Work Status';
    document.getElementById('uploadFile').value = '';

    const modal = new bootstrap.Modal(document.getElementById('uploadModal'));
    modal.show();
}

function logout() {
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    location.reload();
}

function showSection(section) {
    console.log('showSection called with:', section);

    try {
        // Hide all content sections
        const sections = ['dashboardContent', 'newApplicationsContent', 'workStatusContent', 'userManagementContent'];
        sections.forEach(s => {
            const element = document.getElementById(s);
            if (element) {
                element.classList.add('d-none');
            }
        });

        // Remove active class from all nav items
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });

        // Show selected section and set nav active
        currentSection = section;
        const navElement = document.getElementById('nav-' + section);
        if (navElement) {
            navElement.classList.add('active');
        }

        switch (section) {
            case 'dashboard':
                const dashboardElement = document.getElementById('dashboardContent');
                if (dashboardElement) {
                    dashboardElement.classList.remove('d-none');
                    document.getElementById('pageTitle').textContent = 'Dashboard';
                    loadDashboard();
                }
                break;
            case 'newApplications':
                const newAppsElement = document.getElementById('newApplicationsContent');
                if (newAppsElement) {
                    newAppsElement.classList.remove('d-none');
                    document.getElementById('pageTitle').textContent = 'New Water Course Applications';
                    loadNewApplications();
                }
                break;
            case 'workStatus':
                const workStatusElement = document.getElementById('workStatusContent');
                if (workStatusElement) {
                    workStatusElement.classList.remove('d-none');
                    document.getElementById('pageTitle').textContent = 'Work Status Monitoring';
                    loadWorkStatus();
                }
                break;
            case 'userManagement':
                if (currentUser && currentUser.role === 'xen') {
                    const userMgmtElement = document.getElementById('userManagementContent');
                    if (userMgmtElement) {
                        userMgmtElement.classList.remove('d-none');
                        document.getElementById('pageTitle').textContent = 'User Management';
                        loadUserManagement();
                    }
                }
                break;
        }

        console.log('showSection completed for:', section);

    } catch (error) {
        console.error('Error in showSection:', error);
        alert('Error switching sections: ' + error.message);
    }
}

// Make functions globally accessible
window.showAddApplicationModal = showAddApplicationModal;
window.showAddWorkModal = showAddWorkModal;
window.downloadTemplate = downloadTemplate;
window.showUploadModal = showUploadModal;
window.logout = logout;
window.showSection = showSection;
window.editApplication = editApplication;
window.editWork = editWork;
window.editUser = editUser;
window.deleteApplication = deleteApplication;
window.deleteWork = deleteWork;
window.saveApplication = saveApplication;
window.saveWork = saveWork;
window.saveUser = saveUser;
window.uploadFile = uploadFile;

console.log('All functions defined and made global');

// Load dashboard data
async function loadDashboard() {
    try {
        const [newAppsResponse, workStatusResponse] = await Promise.all([
            fetch('/api/new-applications', {
                headers: { 'Authorization': `Bearer ${authToken}` }
            }),
            fetch('/api/work-status', {
                headers: { 'Authorization': `Bearer ${authToken}` }
            })
        ]);

        const newApps = await newAppsResponse.json();
        const workStatus = await workStatusResponse.json();

        document.getElementById('newAppsCount').textContent = newApps.length;
        document.getElementById('activeWorksCount').textContent = workStatus.length;

    } catch (error) {
        console.error('Error loading dashboard:', error);
    }
}

// Load new applications
async function loadNewApplications() {
    console.log('Loading new applications...');
    try {
        const response = await fetch('/api/new-applications', {
            headers: { 'Authorization': `Bearer ${authToken}` }
        });

        if (!response.ok) {
            console.error('API response not OK:', response.status);
            alert('Failed to load applications.');
            return;
        }

        const applications = await response.json();
        console.log('Applications loaded:', applications.length, 'items');

        displayApplicationsTable(applications);

        // Show search for Xen users
        if (currentUser.role === 'xen') {
            document.getElementById('xenSearchContainer').classList.remove('d-none');
        }

        console.log('New applications section loaded successfully');

    } catch (error) {
        console.error('Error loading applications:', error);
        alert('Error loading applications: ' + error.message);
    }
}

// Load work status
async function loadWorkStatus() {
    console.log('Loading work status...');
    try {
        const response = await fetch('/api/work-status', {
            headers: { 'Authorization': `Bearer ${authToken}` }
        });

        if (!response.ok) {
            console.error('API response not OK:', response.status);
            alert('Failed to load work status.');
            return;
        }

        const works = await response.json();
        console.log('Works loaded:', works.length, 'items');

        displayWorkStatusTable(works);

        // Show search for Xen users
        if (currentUser.role === 'xen') {
            document.getElementById('xenSearchContainer2').classList.remove('d-none');
        }

        console.log('Work status section loaded successfully');

    } catch (error) {
        console.error('Error loading work status:', error);
        alert('Error loading work status: ' + error.message);
    }
}

// Load user management (Xen only)
async function loadUserManagement() {
    if (currentUser.role !== 'xen') return;

    try {
        const response = await fetch('/api/users', {
            headers: { 'Authorization': `Bearer ${authToken}` }
        });

        const users = await response.json();
        displayUsersTable(users);

    } catch (error) {
        console.error('Error loading users:', error);
    }
}

// Display applications table
function displayApplicationsTable(applications) {
    const headerRow = document.getElementById('applicationsTableHeader');
    const tbody = document.getElementById('applicationsTableBody');

    // Define headers
    let headers = ['Sr. No', 'Village', 'Constituency', 'District', 'RD', 'Minor', 'Status', 'Work Type', 'Farmer Name', 'Contact', 'Reference'];

    if (currentUser.role === 'xen') {
        headers.push('Subdivision');
    }

    if (currentUser.role === 'subdivision') {
        headers.push('Actions');
    }

    // Build header
    headerRow.innerHTML = headers.map(header => `<th>${header}</th>`).join('');

    // Build table body
    tbody.innerHTML = applications.map(app => {
        let row = `
            <tr>
                <td>${app.sr_no || ''}</td>
                <td>${app.village || ''}</td>
                <td>${app.constituency || ''}</td>
                <td>${app.district || ''}</td>
                <td>${app.rd || ''}</td>
                <td>${app.minor || ''}</td>
                <td><span class="badge status-${getStatusClass(app.status)}">${app.status || ''}</span></td>
                <td>${app.work_type || ''}</td>
                <td>${app.farmer_name || ''}</td>
                <td>${app.contact || ''}</td>
                <td>${app.reference || ''}</td>
        `;

        if (currentUser.role === 'xen') {
            row += `<td>${app.subdivision || ''}</td>`;
        }

        if (currentUser.role === 'subdivision') {
            row += `
                <td>
                    <button class="btn btn-sm btn-primary me-1" onclick="editApplication('${app.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteApplication('${app.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
        }

        row += '</tr>';
        return row;
    }).join('');
}

// Display work status table
function displayWorkStatusTable(works) {
    const headerRow = document.getElementById('workStatusTableHeader');
    const tbody = document.getElementById('workStatusTableBody');

    // Define headers
    let headers = ['Village', 'Constituency', 'District', 'Water Course', 'Work Name', 'AA No. & Date', 'Type of Work', 'Total Length', 'Length Lined', 'Contractor', 'Remarks'];

    if (currentUser.role === 'xen') {
        headers.push('Subdivision');
    }

    if (currentUser.role === 'subdivision') {
        headers.push('Actions');
    }

    // Build header
    headerRow.innerHTML = headers.map(header => `<th>${header}</th>`).join('');

    // Build table body
    tbody.innerHTML = works.map(work => {
        let row = `
            <tr>
                <td>${work.village || ''}</td>
                <td>${work.constituency || ''}</td>
                <td>${work.district || ''}</td>
                <td>${work.water_course || ''}</td>
                <td>${work.work_name || ''}</td>
                <td>${work.aa_no_date || ''}</td>
                <td>${work.type_of_work || ''}</td>
                <td>${work.total_length_to_be_lined || ''}</td>
                <td>${work.length_lined || ''}</td>
                <td>${work.contractor || ''}</td>
                <td>${work.remarks || ''}</td>
        `;

        if (currentUser.role === 'xen') {
            row += `<td>${work.subdivision || ''}</td>`;
        }

        if (currentUser.role === 'subdivision') {
            row += `
                <td>
                    <button class="btn btn-sm btn-primary me-1" onclick="editWork('${work.id}')">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteWork('${work.id}')">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
        }

        row += '</tr>';
        return row;
    }).join('');
}

// Display users table
function displayUsersTable(users) {
    const tbody = document.getElementById('usersTableBody');

    tbody.innerHTML = users.map(user => `
        <tr>
            <td>${user.subdivision}</td>
            <td>${user.username}</td>
            <td>
                <button class="btn btn-sm btn-primary" onclick="editUser('${user.id}', '${user.username}', '${user.subdivision}')">
                    <i class="fas fa-edit me-1"></i>Edit Credentials
                </button>
            </td>
        </tr>
    `).join('');
}

// Get status CSS class
function getStatusClass(status) {
    if (!status) return 'pending';
    const statusLower = status.toLowerCase();
    if (statusLower.includes('approved')) return 'approved';
    if (statusLower.includes('rejected')) return 'rejected';
    if (statusLower.includes('progress')) return 'in-progress';
    return 'pending';
}

// Placeholder functions for edit/delete operations
function editApplication(id) {
    console.log('Edit application:', id);
    alert('Edit application functionality - ID: ' + id);
}

function editWork(id) {
    console.log('Edit work:', id);
    alert('Edit work functionality - ID: ' + id);
}

function editUser(userId, username, subdivision) {
    console.log('Edit user:', userId, username, subdivision);
    alert('Edit user functionality - User: ' + username);
}

function deleteApplication(id) {
    if (confirm('Are you sure you want to delete this application?')) {
        console.log('Delete application:', id);
        alert('Delete application functionality - ID: ' + id);
    }
}

function deleteWork(id) {
    if (confirm('Are you sure you want to delete this work?')) {
        console.log('Delete work:', id);
        alert('Delete work functionality - ID: ' + id);
    }
}

// Save functions for modals
function saveApplication() {
    console.log('Save application called');
    alert('Save application functionality - Form data would be saved here');
}

function saveWork() {
    console.log('Save work called');
    alert('Save work functionality - Form data would be saved here');
}

function saveUser() {
    console.log('Save user called');
    alert('Save user functionality - User credentials would be updated here');
}

function uploadFile() {
    console.log('Upload file called');
    alert('Upload file functionality - File would be processed here');
}

// Check if user is already authenticated
function checkAuthStatus() {
    const token = localStorage.getItem('authToken');
    const user = localStorage.getItem('currentUser');

    if (token && user) {
        authToken = token;
        currentUser = JSON.parse(user);
        showMainApp();
    } else {
        showLoginModal();
    }
}

// Show login modal
function showLoginModal() {
    const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
    loginModal.show();
}

// Show main application
function showMainApp() {
    document.getElementById('mainApp').classList.remove('d-none');
    document.getElementById('userInfo').textContent = `Welcome, ${currentUser.name}`;

    setupNavigation();
    loadDashboard();
}

// Setup navigation based on user role
function setupNavigation() {
    const sidebarNav = document.getElementById('sidebarNav');
    let navItems = [];

    navItems.push({
        id: 'dashboard',
        icon: 'fas fa-tachometer-alt',
        text: 'Dashboard',
        onclick: 'showSection("dashboard")'
    });

    navItems.push({
        id: 'newApplications',
        icon: 'fas fa-file-alt',
        text: 'New Applications',
        onclick: 'showSection("newApplications")'
    });

    navItems.push({
        id: 'workStatus',
        icon: 'fas fa-tools',
        text: 'Work Status',
        onclick: 'showSection("workStatus")'
    });

    if (currentUser.role === 'xen') {
        navItems.push({
            id: 'userManagement',
            icon: 'fas fa-users-cog',
            text: 'Manage Users',
            onclick: 'showSection("userManagement")'
        });
    }

    sidebarNav.innerHTML = navItems.map(item => `
        <li class="nav-item">
            <a class="nav-link" href="#" id="nav-${item.id}" onclick="${item.onclick}">
                <i class="${item.icon}"></i>
                ${item.text}
            </a>
        </li>
    `).join('');

    document.getElementById('nav-dashboard').classList.add('active');
}

// Handle login
async function handleLogin(e) {
    e.preventDefault();

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const errorDiv = document.getElementById('loginError');

    try {
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });

        const data = await response.json();

        if (response.ok) {
            authToken = data.token;
            currentUser = data.user;

            localStorage.setItem('authToken', authToken);
            localStorage.setItem('currentUser', JSON.stringify(currentUser));

            const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
            if (loginModal) {
                loginModal.hide();
            }
            showMainApp();
        } else {
            errorDiv.textContent = data.error;
            errorDiv.classList.remove('d-none');
        }
    } catch (error) {
        console.error('Login error:', error);
        errorDiv.textContent = 'Connection error. Please try again.';
        errorDiv.classList.remove('d-none');
    }
}

// Basic initialization
document.addEventListener('DOMContentLoaded', function () {
    console.log('DOM loaded with minimal script');
    checkAuthStatus();

    // Setup login form
    document.getElementById('loginForm').addEventListener('submit', handleLogin);
});
