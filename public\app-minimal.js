// MICADA Division Monitoring System - Minimal Working Version

// Global variables
let currentUser = null;
let authToken = null;
let currentSection = 'dashboard';

// Essential functions only
function showAddApplicationModal() {
    console.log('showAddApplicationModal called');
    try {
        const modalElement = document.getElementById('applicationModal');
        if (!modalElement) {
            alert('Modal not found. Please refresh the page.');
            return;
        }

        document.getElementById('applicationModalTitle').textContent = 'Add New Application';
        document.getElementById('applicationForm').reset();
        document.getElementById('applicationId').value = '';

        const modal = new bootstrap.Modal(modalElement);
        modal.show();
        console.log('Modal shown successfully');
    } catch (error) {
        console.error('Error showing modal:', error);
        alert('Error opening form: ' + error.message);
    }
}

function showAddWorkModal() {
    console.log('showAddWorkModal called');
    try {
        document.getElementById('workModalTitle').textContent = 'Add New Work';
        document.getElementById('workForm').reset();
        document.getElementById('workId').value = '';

        const modal = new bootstrap.Modal(document.getElementById('workModal'));
        modal.show();
        console.log('Work modal shown');
    } catch (error) {
        console.error('Error showing work modal:', error);
        alert('Error opening form: ' + error.message);
    }
}

function downloadTemplate(type) {
    console.log('downloadTemplate called with type:', type);
    let headers, filename;

    if (type === 'applications') {
        headers = ['Sr. No', 'Village', 'Constituency', 'District', 'RD', 'MINOR', 'Status', 'Work Type', "Farmer's Name", 'Contact', 'Reference'];
        filename = 'new_applications_template.csv';
    } else if (type === 'works') {
        headers = ['Village', 'Constituency', 'District', 'Water Course Name', 'Name of Work', 'AA No. & Date', 'Type of Work', 'Total Length to be Lined', 'Length Lined', 'Contractor', 'Remarks'];
        filename = 'work_status_template.csv';
    }

    const csvContent = headers.join(',') + '\n';
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

function showUploadModal(type) {
    console.log('showUploadModal called with type:', type);
    document.getElementById('uploadType').value = type;
    document.getElementById('uploadModalTitle').textContent =
        type === 'applications' ? 'Bulk Upload Applications' : 'Bulk Upload Work Status';
    document.getElementById('uploadFile').value = '';

    const modal = new bootstrap.Modal(document.getElementById('uploadModal'));
    modal.show();
}

function logout() {
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    location.reload();
}

function showSection(section) {
    console.log('showSection called with:', section);

    try {
        // Hide all content sections
        const sections = ['dashboardContent', 'newApplicationsContent', 'workStatusContent', 'userManagementContent'];
        sections.forEach(s => {
            const element = document.getElementById(s);
            if (element) {
                element.classList.add('d-none');
            }
        });

        // Remove active class from all nav items
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });

        // Show selected section and set nav active
        currentSection = section;
        const navElement = document.getElementById('nav-' + section);
        if (navElement) {
            navElement.classList.add('active');
        }

        switch (section) {
            case 'dashboard':
                const dashboardElement = document.getElementById('dashboardContent');
                if (dashboardElement) {
                    dashboardElement.classList.remove('d-none');
                    document.getElementById('pageTitle').textContent = 'Dashboard';
                    loadDashboard();
                }
                break;
            case 'newApplications':
                const newAppsElement = document.getElementById('newApplicationsContent');
                if (newAppsElement) {
                    newAppsElement.classList.remove('d-none');
                    document.getElementById('pageTitle').textContent = 'New Water Course Applications';
                    loadNewApplications();
                }
                break;
            case 'workStatus':
                const workStatusElement = document.getElementById('workStatusContent');
                if (workStatusElement) {
                    workStatusElement.classList.remove('d-none');
                    document.getElementById('pageTitle').textContent = 'Work Status Monitoring';
                    loadWorkStatus();
                }
                break;
            case 'userManagement':
                if (currentUser && currentUser.role === 'xen') {
                    const userMgmtElement = document.getElementById('userManagementContent');
                    if (userMgmtElement) {
                        userMgmtElement.classList.remove('d-none');
                        document.getElementById('pageTitle').textContent = 'User Management';
                        loadUserManagement();
                    }
                }
                break;
        }

        console.log('showSection completed for:', section);

    } catch (error) {
        console.error('Error in showSection:', error);
        alert('Error switching sections: ' + error.message);
    }
}

// Make functions globally accessible
window.showAddApplicationModal = showAddApplicationModal;
window.showAddWorkModal = showAddWorkModal;
window.downloadTemplate = downloadTemplate;
window.showUploadModal = showUploadModal;
window.logout = logout;
window.showSection = showSection;

console.log('Minimal functions defined and made global');

// Placeholder functions for missing ones
function loadDashboard() {
    console.log('loadDashboard called');
}

function loadNewApplications() {
    console.log('loadNewApplications called');
}

function loadWorkStatus() {
    console.log('loadWorkStatus called');
}

function loadUserManagement() {
    console.log('loadUserManagement called');
}

// Check if user is already authenticated
function checkAuthStatus() {
    const token = localStorage.getItem('authToken');
    const user = localStorage.getItem('currentUser');

    if (token && user) {
        authToken = token;
        currentUser = JSON.parse(user);
        showMainApp();
    } else {
        showLoginModal();
    }
}

// Show login modal
function showLoginModal() {
    const loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
    loginModal.show();
}

// Show main application
function showMainApp() {
    document.getElementById('mainApp').classList.remove('d-none');
    document.getElementById('userInfo').textContent = `Welcome, ${currentUser.name}`;

    setupNavigation();
    loadDashboard();
}

// Setup navigation based on user role
function setupNavigation() {
    const sidebarNav = document.getElementById('sidebarNav');
    let navItems = [];

    navItems.push({
        id: 'dashboard',
        icon: 'fas fa-tachometer-alt',
        text: 'Dashboard',
        onclick: 'showSection("dashboard")'
    });

    navItems.push({
        id: 'newApplications',
        icon: 'fas fa-file-alt',
        text: 'New Applications',
        onclick: 'showSection("newApplications")'
    });

    navItems.push({
        id: 'workStatus',
        icon: 'fas fa-tools',
        text: 'Work Status',
        onclick: 'showSection("workStatus")'
    });

    if (currentUser.role === 'xen') {
        navItems.push({
            id: 'userManagement',
            icon: 'fas fa-users-cog',
            text: 'Manage Users',
            onclick: 'showSection("userManagement")'
        });
    }

    sidebarNav.innerHTML = navItems.map(item => `
        <li class="nav-item">
            <a class="nav-link" href="#" id="nav-${item.id}" onclick="${item.onclick}">
                <i class="${item.icon}"></i>
                ${item.text}
            </a>
        </li>
    `).join('');

    document.getElementById('nav-dashboard').classList.add('active');
}

// Handle login
async function handleLogin(e) {
    e.preventDefault();

    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    const errorDiv = document.getElementById('loginError');

    try {
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });

        const data = await response.json();

        if (response.ok) {
            authToken = data.token;
            currentUser = data.user;

            localStorage.setItem('authToken', authToken);
            localStorage.setItem('currentUser', JSON.stringify(currentUser));

            const loginModal = bootstrap.Modal.getInstance(document.getElementById('loginModal'));
            if (loginModal) {
                loginModal.hide();
            }
            showMainApp();
        } else {
            errorDiv.textContent = data.error;
            errorDiv.classList.remove('d-none');
        }
    } catch (error) {
        console.error('Login error:', error);
        errorDiv.textContent = 'Connection error. Please try again.';
        errorDiv.classList.remove('d-none');
    }
}

// Basic initialization
document.addEventListener('DOMContentLoaded', function () {
    console.log('DOM loaded with minimal script');
    checkAuthStatus();

    // Setup login form
    document.getElementById('loginForm').addEventListener('submit', handleLogin);
});
