<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Button Test</h2>
        
        <button class="btn btn-success me-2" onclick="testFunction()">
            Test Button 1
        </button>
        
        <button class="btn btn-info me-2" onclick="window.testFunction2()">
            Test Button 2
        </button>
        
        <button class="btn btn-warning" onclick="alert('Direct onclick works!')">
            Test Button 3
        </button>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function testFunction() {
            alert('Test function 1 works!');
        }
        
        window.testFunction2 = function() {
            alert('Test function 2 works!');
        };
        
        console.log('Test page loaded');
    </script>
</body>
</html>
