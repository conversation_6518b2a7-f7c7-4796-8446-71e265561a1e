<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Authentication - MICADA</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3>Authentication Test</h3>
                    </div>
                    <div class="card-body">
                        <button id="testSignIn" class="btn btn-primary">Test Sign In</button>
                        <button id="testDatabase" class="btn btn-success">Test Database</button>
                        <button id="signOutBtn" class="btn btn-danger">Sign Out</button>
                        
                        <div id="output" class="mt-4 p-3 bg-light" style="height: 400px; overflow-y: auto; font-family: monospace; font-size: 12px;">
                            Test output will appear here...<br>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getAuth, GoogleAuthProvider, signInWithPopup, signOut, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js";
        import { getFirestore, collection, getDocs, query, where } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyC4bwhHbZ9oc6mPkzIbQKJyIk9sSiA-S9s",
            authDomain: "micada-division.firebaseapp.com",
            projectId: "micada-division",
            storageBucket: "micada-division.appspot.com",
            messagingSenderId: "363841115848",
            appId: "1:363841115848:web:68ce295c4375e68fe077fd",
            measurementId: "G-1RXZT0K512"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);
        const provider = new GoogleAuthProvider();

        function log(message) {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}<br>`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        // Test sign in
        document.getElementById('testSignIn').addEventListener('click', async () => {
            try {
                log('🔐 Starting Google Sign-in test...');
                const result = await signInWithPopup(auth, provider);
                const user = result.user;
                log('✅ Sign-in successful!');
                log(`User: ${user.email}`);
                log(`Display Name: ${user.displayName}`);
                log(`UID: ${user.uid}`);
                
                // Test authorization
                if (user.email === '<EMAIL>') {
                    log('✅ XEN user detected - should have full access');
                } else {
                    log('🔍 Checking if user is in authorized_users collection...');
                    await testUserAuthorization(user.email);
                }
                
            } catch (error) {
                log('❌ Sign-in failed: ' + error.message);
                log('Error code: ' + error.code);
            }
        });

        // Test database
        document.getElementById('testDatabase').addEventListener('click', async () => {
            try {
                log('🔍 Testing database connection...');
                
                // Test reading subdivisions
                const subdivisionsSnapshot = await getDocs(collection(db, 'subdivisions'));
                log(`✅ Subdivisions collection: ${subdivisionsSnapshot.size} documents`);
                
                // Test reading authorized users
                const usersSnapshot = await getDocs(collection(db, 'authorized_users'));
                log(`✅ Authorized users collection: ${usersSnapshot.size} documents`);
                
                // List authorized users
                usersSnapshot.forEach((doc) => {
                    const userData = doc.data();
                    log(`  - ${userData.email} (${userData.subdivision})`);
                });
                
                // Test reading applications
                const appsSnapshot = await getDocs(collection(db, 'new_applications'));
                log(`✅ Applications collection: ${appsSnapshot.size} documents`);
                
                // Test reading work status
                const worksSnapshot = await getDocs(collection(db, 'work_status'));
                log(`✅ Work status collection: ${worksSnapshot.size} documents`);
                
            } catch (error) {
                log('❌ Database test failed: ' + error.message);
                log('Error code: ' + error.code);
            }
        });

        // Test user authorization
        async function testUserAuthorization(email) {
            try {
                const authorizedUsersQuery = query(
                    collection(db, 'authorized_users'), 
                    where('email', '==', email), 
                    where('active', '==', true)
                );
                const querySnapshot = await getDocs(authorizedUsersQuery);
                
                if (!querySnapshot.empty) {
                    const userDoc = querySnapshot.docs[0];
                    const userData = userDoc.data();
                    log(`✅ User found in authorized_users:`);
                    log(`  - Role: ${userData.role}`);
                    log(`  - Subdivision: ${userData.subdivision}`);
                    log(`  - Active: ${userData.active}`);
                } else {
                    log(`❌ User ${email} not found in authorized_users collection`);
                }
            } catch (error) {
                log('❌ Error checking user authorization: ' + error.message);
            }
        }

        // Sign out
        document.getElementById('signOutBtn').addEventListener('click', async () => {
            try {
                await signOut(auth);
                log('👋 Signed out successfully');
            } catch (error) {
                log('❌ Sign out failed: ' + error.message);
            }
        });

        // Listen for auth state changes
        onAuthStateChanged(auth, (user) => {
            if (user) {
                log(`👤 Auth state: User signed in (${user.email})`);
            } else {
                log('👤 Auth state: User signed out');
            }
        });

        log('🔥 Firebase test page loaded successfully');
        log('Click "Test Sign In" to test Google authentication');
        log('Click "Test Database" to test Firestore connection');
    </script>
</body>
</html>
