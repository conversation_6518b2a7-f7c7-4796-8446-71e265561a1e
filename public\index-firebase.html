<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MICADA Division Monitoring System</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="styles.css" rel="stylesheet">
</head>

<body>
    <!-- Login Screen -->
    <div id="loginScreen" class="d-flex align-items-center justify-content-center min-vh-100 bg-light">
        <div class="card shadow-lg" style="width: 400px;">
            <div class="card-body text-center p-5">
                <div class="mb-4">
                    <i class="fas fa-water text-primary" style="font-size: 3rem;"></i>
                    <h2 class="mt-3 text-primary">MICADA Division</h2>
                    <p class="text-muted">Monitoring System</p>
                </div>

                <button onclick="signInWithGoogle()" class="btn btn-danger btn-lg w-100">
                    <i class="fab fa-google me-2"></i>
                    Sign in with Google
                </button>

                <div class="mt-4">
                    <small class="text-muted">
                        Only authorized MICADA personnel can access this system
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="mainApp" class="d-none">
        <!-- Header -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-water me-2"></i>
                    MICADA Division Monitoring
                </a>

                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <span id="userInfo">User</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="signOutUser()">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar -->
                <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                    <div class="position-sticky pt-3">
                        <ul class="nav flex-column" id="sidebarNav">
                            <!-- Navigation will be populated by JavaScript -->
                        </ul>
                    </div>
                </nav>

                <!-- Main Content -->
                <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                    <div
                        class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2" id="pageTitle">Dashboard</h1>
                    </div>

                    <!-- Dashboard Content -->
                    <div id="dashboardContent">
                        <div class="row">
                            <div class="col-md-6 col-lg-3 mb-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4 id="newAppsCount">0</h4>
                                                <p class="mb-0">New Applications</p>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-file-alt fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-lg-3 mb-4">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4 id="activeWorksCount">0</h4>
                                                <p class="mb-0">Active Works</p>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-tools fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- New Applications Content -->
                    <div id="newApplicationsContent" class="d-none">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3>New Water Course Applications</h3>
                            <div>
                                <button class="btn btn-primary" onclick="showAddApplicationModal()">
                                    <i class="fas fa-plus me-1"></i>Add Application
                                </button>
                                <button class="btn btn-success" onclick="downloadTemplate('applications')">
                                    <i class="fas fa-download me-1"></i>Download Template
                                </button>
                                <button class="btn btn-info" onclick="showUploadModal('applications')">
                                    <i class="fas fa-upload me-1"></i>Bulk Upload
                                </button>
                            </div>
                        </div>

                        <!-- Search Container for Xen -->
                        <div id="xenSearchContainer" class="d-none mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <input type="text" class="form-control" id="universalSearch"
                                        placeholder="Search across all fields..."
                                        onkeyup="handleUniversalSearch(event)">
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr id="applicationsTableHeader">
                                        <!-- Headers will be populated by JavaScript -->
                                    </tr>
                                </thead>
                                <tbody id="applicationsTableBody">
                                    <!-- Data will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Work Status Content -->
                    <div id="workStatusContent" class="d-none">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3>Work Status Monitoring</h3>
                            <div>
                                <button class="btn btn-primary" onclick="showAddWorkModal()">
                                    <i class="fas fa-plus me-1"></i>Add Work
                                </button>
                                <button class="btn btn-success" onclick="downloadTemplate('works')">
                                    <i class="fas fa-download me-1"></i>Download Template
                                </button>
                                <button class="btn btn-info" onclick="showUploadModal('works')">
                                    <i class="fas fa-upload me-1"></i>Bulk Upload
                                </button>
                            </div>
                        </div>

                        <!-- Search Container for Xen -->
                        <div id="xenSearchContainer2" class="d-none mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <input type="text" class="form-control" id="universalSearchWorks"
                                        placeholder="Search across all fields..."
                                        onkeyup="handleUniversalSearchWorks(event)">
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr id="workStatusTableHeader">
                                        <!-- Headers will be populated by JavaScript -->
                                    </tr>
                                </thead>
                                <tbody id="workStatusTableBody">
                                    <!-- Data will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- User Management Content (Xen only) -->
                    <div id="userManagementContent" class="d-none">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3>User Management</h3>
                            <button class="btn btn-primary" onclick="showAddUserModal()">
                                <i class="fas fa-plus me-1"></i>Add New User
                            </button>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Subdivision</th>
                                        <th>Email</th>
                                        <th>Last Login</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTableBody">
                                    <!-- Data will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm">
                        <div class="mb-3">
                            <label for="userEmail" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="userEmail" required>
                            <div class="form-text">User must have a Google account with this email</div>
                        </div>
                        <div class="mb-3">
                            <label for="userSubdivision" class="form-label">Subdivision</label>
                            <select class="form-select" id="userSubdivision" required>
                                <option value="">Select Subdivision</option>
                                <option value="narwana">MICAD Sub Division, Narwana</option>
                                <option value="barwala">Barwala CAD Subdivision</option>
                                <option value="hisar1">CAD Subdivision 1 Hisar</option>
                                <option value="hisar2">CAD Subdivision 2 Hisar</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="userName" class="form-label">Full Name</label>
                            <input type="text" class="form-control" id="userName" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveNewUser()">Add User</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Firebase Configuration -->
    <script type="module" src="firebase-config.js"></script>

    <!-- Application Logic -->
    <script type="module" src="firebase-app.js"></script>
</body>

</html>