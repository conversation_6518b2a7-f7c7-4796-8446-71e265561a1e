<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MICADA Division - Add New Fields Migration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-warning text-dark">
                        <h4 class="mb-0">
                            <i class="fas fa-database me-2"></i>
                            Add New Fields Migration
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Important:</strong> This migration will add new fields to existing application and work status records:
                            <ul class="mt-2 mb-0">
                                <li><strong>Applications:</strong> Add "% of Micro Irrigation" and "Remarks" fields</li>
                                <li><strong>Work Status:</strong> Ensure proper field order and structure</li>
                                <li>This operation will update all existing records in all subdivision collections</li>
                            </ul>
                        </div>

                        <div class="mb-4">
                            <h5>Migration Progress</h5>
                            <div class="progress mb-2">
                                <div id="progressBar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <div id="progressText" class="text-muted">Ready to start migration...</div>
                        </div>

                        <div class="mb-4">
                            <h5>Migration Log</h5>
                            <div id="migrationLog" class="border rounded p-3 bg-light" style="height: 300px; overflow-y: auto;">
                                <p class="text-muted">Migration log will appear here...</p>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <button id="startMigration" class="btn btn-warning btn-lg">
                                <i class="fas fa-play me-2"></i>Start Migration
                            </button>
                            <button id="backToApp" class="btn btn-secondary" onclick="window.location.href='index.html'">
                                <i class="fas fa-arrow-left me-2"></i>Back to Application
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, getDocs, updateDoc, doc } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyC4bwhHbZ9oc6mPkzIbQKJyIk9sSiA-S9s",
            authDomain: "micada-division.firebaseapp.com",
            projectId: "micada-division",
            storageBucket: "micada-division.appspot.com",
            messagingSenderId: "363841115848",
            appId: "1:363841115848:web:68ce295c4375e68fe077fd",
            measurementId: "G-1RXZT0K512"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        // Helper functions
        function getApplicationsCollectionName(subdivision) {
            return `applications_${subdivision}`;
        }

        function getWorkStatusCollectionName(subdivision) {
            return `work_status_${subdivision}`;
        }

        function getAllSubdivisions() {
            return ['narwana', 'barwala', 'hisar1', 'hisar2'];
        }

        function log(message, type = 'info') {
            const logDiv = document.getElementById('migrationLog');
            const timestamp = new Date().toLocaleTimeString();
            const icon = type === 'error' ? 'fas fa-times-circle text-danger' : 
                        type === 'success' ? 'fas fa-check-circle text-success' : 
                        'fas fa-info-circle text-primary';
            
            logDiv.innerHTML += `
                <div class="mb-1">
                    <small class="text-muted">[${timestamp}]</small>
                    <i class="${icon} me-1"></i>
                    ${message}
                </div>
            `;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateProgress(current, total, text) {
            const percentage = Math.round((current / total) * 100);
            document.getElementById('progressBar').style.width = percentage + '%';
            document.getElementById('progressBar').textContent = percentage + '%';
            document.getElementById('progressText').textContent = text;
        }

        // Migration function
        async function startMigration() {
            const startButton = document.getElementById('startMigration');
            startButton.disabled = true;
            startButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Migrating...';

            log('Starting migration to add new fields...', 'info');
            
            try {
                const subdivisions = getAllSubdivisions();
                let totalOperations = 0;
                let completedOperations = 0;

                // Count total operations first
                for (const subdivision of subdivisions) {
                    try {
                        const appsSnapshot = await getDocs(collection(db, getApplicationsCollectionName(subdivision)));
                        const worksSnapshot = await getDocs(collection(db, getWorkStatusCollectionName(subdivision)));
                        totalOperations += appsSnapshot.size + worksSnapshot.size;
                    } catch (error) {
                        log(`No collections found for ${subdivision}`, 'info');
                    }
                }

                log(`Found ${totalOperations} total records to migrate`, 'info');

                // Migrate applications
                for (const subdivision of subdivisions) {
                    try {
                        const collectionName = getApplicationsCollectionName(subdivision);
                        const snapshot = await getDocs(collection(db, collectionName));
                        
                        log(`Migrating ${snapshot.size} applications in ${subdivision}...`, 'info');

                        for (const docSnapshot of snapshot.docs) {
                            const data = docSnapshot.data();
                            const updates = {};

                            // Add new fields if they don't exist
                            if (!data.hasOwnProperty('micro_irrigation_percent')) {
                                updates.micro_irrigation_percent = 0;
                            }
                            if (!data.hasOwnProperty('application_remarks')) {
                                updates.application_remarks = '';
                            }

                            // Update if there are changes
                            if (Object.keys(updates).length > 0) {
                                await updateDoc(doc(db, collectionName, docSnapshot.id), updates);
                                log(`Updated application ${docSnapshot.id} in ${subdivision}`, 'success');
                            }

                            completedOperations++;
                            updateProgress(completedOperations, totalOperations, 
                                `Migrating applications in ${subdivision}... (${completedOperations}/${totalOperations})`);
                        }

                        log(`Completed applications migration for ${subdivision}`, 'success');

                    } catch (error) {
                        log(`Error migrating applications in ${subdivision}: ${error.message}`, 'error');
                    }
                }

                // Migrate work status records
                for (const subdivision of subdivisions) {
                    try {
                        const collectionName = getWorkStatusCollectionName(subdivision);
                        const snapshot = await getDocs(collection(db, collectionName));
                        
                        log(`Migrating ${snapshot.size} work records in ${subdivision}...`, 'info');

                        for (const docSnapshot of snapshot.docs) {
                            const data = docSnapshot.data();
                            const updates = {};

                            // Ensure all required fields exist with proper names
                            const fieldMappings = {
                                'water_course_rd': data.water_course_rd || data.water_course || '',
                                'channel_name': data.channel_name || data.work_name || '',
                                'physical_progress': data.physical_progress || 0,
                                'work_status': data.work_status || data.status || '',
                                'aa_no_date': data.aa_no_date || '',
                                'type_of_work': data.type_of_work || '',
                                'total_length_to_be_lined': data.total_length_to_be_lined || '',
                                'length_lined': data.length_lined || '',
                                'contractor': data.contractor || '',
                                'junior_engineer': data.junior_engineer || '',
                                'remarks': data.remarks || '',
                                'village': data.village || '',
                                'constituency': data.constituency || '',
                                'district': data.district || ''
                            };

                            // Check if any field needs updating
                            let needsUpdate = false;
                            for (const [field, value] of Object.entries(fieldMappings)) {
                                if (data[field] !== value) {
                                    updates[field] = value;
                                    needsUpdate = true;
                                }
                            }

                            // Update if there are changes
                            if (needsUpdate) {
                                await updateDoc(doc(db, collectionName, docSnapshot.id), updates);
                                log(`Updated work record ${docSnapshot.id} in ${subdivision}`, 'success');
                            }

                            completedOperations++;
                            updateProgress(completedOperations, totalOperations, 
                                `Migrating work records in ${subdivision}... (${completedOperations}/${totalOperations})`);
                        }

                        log(`Completed work records migration for ${subdivision}`, 'success');

                    } catch (error) {
                        log(`Error migrating work records in ${subdivision}: ${error.message}`, 'error');
                    }
                }

                updateProgress(100, 100, 'Migration completed successfully!');
                log('Migration completed successfully! All records have been updated with new fields.', 'success');
                
                startButton.innerHTML = '<i class="fas fa-check me-2"></i>Migration Complete';
                startButton.classList.remove('btn-warning');
                startButton.classList.add('btn-success');

            } catch (error) {
                log(`Migration failed: ${error.message}`, 'error');
                startButton.disabled = false;
                startButton.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>Retry Migration';
                startButton.classList.add('btn-danger');
            }
        }

        // Event listeners
        document.getElementById('startMigration').addEventListener('click', startMigration);
    </script>
</body>
</html>
