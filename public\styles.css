/* MICADA Division Monitoring System Styles */

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.sidebar {
    position: fixed;
    top: 56px;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
}

.sidebar-sticky {
    position: relative;
    top: 0;
    height: calc(100vh - 48px);
    padding-top: .5rem;
    overflow-x: hidden;
    overflow-y: auto;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: 12px 16px;
    border-radius: 8px;
    margin: 4px 8px;
    transition: all 0.3s ease;
}

.sidebar .nav-link:hover {
    color: #007bff;
    background-color: #e9ecef;
}

.sidebar .nav-link.active {
    color: #007bff;
    background-color: #e7f3ff;
    border-left: 4px solid #007bff;
}

.sidebar .nav-link .fas {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

main {
    margin-left: 240px;
    padding-top: 20px;
}

@media (max-width: 767.98px) {
    .sidebar {
        top: 5rem;
    }

    main {
        margin-left: 0;
    }
}

.card {
    border: none;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.table thead th {
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 8px 16px;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0;
}

.form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 10px 12px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.navbar-brand {
    font-weight: 600;
    font-size: 1.25rem;
}

.alert {
    border: none;
    border-radius: 8px;
}

.badge {
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 6px;
}

/* Status badges */
.status-pending {
    background-color: #ffc107;
    color: #000;
}

.status-approved {
    background-color: #28a745;
    color: #fff;
}

.status-rejected {
    background-color: #dc3545;
    color: #fff;
}

.status-in-progress {
    background-color: #17a2b8;
    color: #fff;
}

/* Loading spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Search highlight */
.search-highlight {
    background-color: #fff3cd;
    padding: 2px 4px;
    border-radius: 3px;
}

/* Responsive table */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 0.875rem;
    }

    .btn {
        padding: 6px 12px;
        font-size: 0.875rem;
    }
}

/* Custom scrollbar */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.sidebar::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Animation for content switching */
.content-section {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Print styles */
@media print {

    .sidebar,
    .navbar,
    .btn,
    .modal {
        display: none !important;
    }

    main {
        margin-left: 0 !important;
    }

    .table {
        font-size: 12px;
    }
}

/* Chart container styles */
.chart-container {
    position: relative;
    height: 250px;
    width: 100%;
    overflow: hidden;
}

.chart-container canvas {
    max-height: 100% !important;
    max-width: 100% !important;
}

/* Prevent chart overflow */
.card-body canvas {
    max-height: 250px !important;
    max-width: 100% !important;
}

/* Chart responsive behavior */
@media (max-width: 768px) {
    .chart-container {
        height: 200px;
    }

    .card-body canvas {
        max-height: 200px !important;
    }
}