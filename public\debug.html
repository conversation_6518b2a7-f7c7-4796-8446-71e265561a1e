<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MICADA Debug Page</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>MICADA System Debug Page</h2>
        <p>This page helps diagnose issues with the MICADA system.</p>
        
        <div class="row">
            <div class="col-md-6">
                <h4>Test Buttons</h4>
                
                <button class="btn btn-primary mb-2 d-block" onclick="testBootstrap()">
                    Test Bootstrap Modal
                </button>
                
                <button class="btn btn-success mb-2 d-block" onclick="testApplicationModal()">
                    Test Application Modal
                </button>
                
                <button class="btn btn-info mb-2 d-block" onclick="testAPI()">
                    Test API Connection
                </button>
                
                <button class="btn btn-warning mb-2 d-block" onclick="testLogin()">
                    Test Login API
                </button>
            </div>
            
            <div class="col-md-6">
                <h4>Debug Output</h4>
                <div id="debugOutput" style="background: #f8f9fa; padding: 15px; height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;">
                    Debug output will appear here...<br>
                </div>
            </div>
        </div>
    </div>

    <!-- Test Modal -->
    <div class="modal fade" id="testModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Test Modal</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>This is a test modal. If you can see this, Bootstrap modals are working!</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Application Modal (copied from main page) -->
    <div class="modal fade" id="applicationModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="applicationModalTitle">Add New Application</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="applicationForm">
                        <input type="hidden" id="applicationId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="sr_no" class="form-label">Sr. No</label>
                                <input type="text" class="form-control" id="sr_no" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="village" class="form-label">Village</label>
                                <input type="text" class="form-control" id="village" required>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary">Save Application</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function log(message) {
            const output = document.getElementById('debugOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}<br>`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        function testBootstrap() {
            log('Testing Bootstrap modal...');
            try {
                const modal = new bootstrap.Modal(document.getElementById('testModal'));
                modal.show();
                log('✓ Bootstrap modal test successful');
            } catch (error) {
                log('✗ Bootstrap modal test failed: ' + error.message);
            }
        }

        function testApplicationModal() {
            log('Testing Application modal...');
            try {
                const modalElement = document.getElementById('applicationModal');
                if (!modalElement) {
                    log('✗ Application modal element not found');
                    return;
                }
                log('✓ Application modal element found');
                
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
                log('✓ Application modal shown successfully');
            } catch (error) {
                log('✗ Application modal test failed: ' + error.message);
            }
        }

        async function testAPI() {
            log('Testing API connection...');
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'test', password: 'test' })
                });
                log('✓ API endpoint reachable (status: ' + response.status + ')');
            } catch (error) {
                log('✗ API connection failed: ' + error.message);
            }
        }

        async function testLogin() {
            log('Testing login with Xen credentials...');
            try {
                const response = await fetch('/api/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        username: 'xenmicadahisar', 
                        password: 'dharmji@1947' 
                    })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    log('✓ Login successful for user: ' + data.user.name);
                    log('✓ Token received: ' + (data.token ? 'Yes' : 'No'));
                } else {
                    const error = await response.json();
                    log('✗ Login failed: ' + error.error);
                }
            } catch (error) {
                log('✗ Login test failed: ' + error.message);
            }
        }

        // Initialize
        log('Debug page loaded');
        log('Bootstrap version: ' + (typeof bootstrap !== 'undefined' ? 'Loaded' : 'Not loaded'));
    </script>
</body>
</html>
