// Firebase Configuration Template
// REPLACE THE CONFIG BELOW WITH YOUR ACTUAL FIREBASE CONFIG

import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
import { getAuth, GoogleAuthProvider, signInWithPopup, signOut, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js";
import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

// 🔥 REPLACE THIS WITH YOUR ACTUAL FIREBASE CONFIG
// Get this from Firebase Console → Project Settings → Your apps → Web app
const firebaseConfig = {
  apiKey: "YOUR_API_KEY_HERE",
  authDomain: "YOUR_PROJECT_ID.firebaseapp.com",
  projectId: "YOUR_PROJECT_ID",
  storageBucket: "YOUR_PROJECT_ID.appspot.com",
  messagingSenderId: "YOUR_MESSAGING_SENDER_ID",
  appId: "YOUR_APP_ID",
  measurementId: "YOUR_MEASUREMENT_ID"
};

// Example of what it should look like:
/*
const firebaseConfig = {
  apiKey: "AIzaSyC4bwhHbZ9oc6mPkzIbQKJyIk9sSiA-S9s",
  authDomain: "micada-division-system-12345.firebaseapp.com",
  projectId: "micada-division-system-12345",
  storageBucket: "micada-division-system-12345.appspot.com",
  messagingSenderId: "363841115848",
  appId: "1:363841115848:web:68ce295c4375e68fe077fd",
  measurementId: "G-1RXZT0K512"
};
*/

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);
const provider = new GoogleAuthProvider();

// Global variables
let currentUser = null;
let userRole = null;
let userSubdivision = null;

// Authentication functions
window.signInWithGoogle = async function() {
    try {
        console.log('🔐 Starting Google Sign-in...');
        const result = await signInWithPopup(auth, provider);
        const user = result.user;
        console.log('✅ User signed in:', user.email);
        
        // Check if user is authorized
        await checkUserAuthorization(user);
        
    } catch (error) {
        console.error('❌ Sign in error:', error);
        
        // Handle specific error cases
        if (error.code === 'auth/popup-closed-by-user') {
            alert('Sign-in was cancelled. Please try again.');
        } else if (error.code === 'auth/unauthorized-domain') {
            alert('This domain is not authorized for Google Sign-in. Please contact the administrator.');
        } else {
            alert('Sign in failed: ' + error.message);
        }
    }
};

window.signOutUser = async function() {
    try {
        await signOut(auth);
        console.log('👋 User signed out');
        showLoginScreen();
    } catch (error) {
        console.error('❌ Sign out error:', error);
    }
};

// Check user authorization
async function checkUserAuthorization(user) {
    const email = user.email;
    console.log('🔍 Checking authorization for:', email);
    
    // Define authorized users - UPDATE THESE EMAIL ADDRESSES
    const authorizedUsers = {
        // XEN Office - Full access to all subdivisions
        '<EMAIL>': { 
            role: 'xen', 
            subdivision: null, 
            name: 'Executive Engineer MICADA' 
        },
        
        // Subdivision Users - Access only to their subdivision data
        '<EMAIL>': { 
            role: 'subdivision', 
            subdivision: 'narwana', 
            name: 'MICAD Sub Division, Narwana' 
        },
        '<EMAIL>': { 
            role: 'subdivision', 
            subdivision: 'barwala', 
            name: 'Barwala CAD Subdivision' 
        },
        '<EMAIL>': { 
            role: 'subdivision', 
            subdivision: 'hisar1', 
            name: 'CAD Subdivision 1 Hisar' 
        },
        '<EMAIL>': { 
            role: 'subdivision', 
            subdivision: 'hisar2', 
            name: 'CAD Subdivision 2 Hisar' 
        },
        
        // 🔧 ADD MORE AUTHORIZED USERS HERE AS NEEDED
        // '<EMAIL>': { 
        //     role: 'subdivision', 
        //     subdivision: 'new_subdivision', 
        //     name: 'New Subdivision Name' 
        // },
    };
    
    if (authorizedUsers[email]) {
        console.log('✅ User authorized:', email);
        currentUser = user;
        userRole = authorizedUsers[email].role;
        userSubdivision = authorizedUsers[email].subdivision;
        
        // Save user info to Firestore
        await saveUserInfo(user, authorizedUsers[email]);
        
        showMainApp();
    } else {
        console.log('❌ User not authorized:', email);
        alert(`Access Denied!\n\nEmail: ${email}\n\nYou are not authorized to access this system.\nPlease contact the MICADA administrator to get access.`);
        await signOut(auth);
    }
}

// Save user info to Firestore
async function saveUserInfo(user, userInfo) {
    try {
        console.log('💾 Saving user info to Firestore...');
        
        // Try to update existing document, create if doesn't exist
        const userDoc = doc(db, 'users', user.uid);
        const userData = {
            uid: user.uid,
            email: user.email,
            name: user.displayName,
            role: userInfo.role,
            subdivision: userInfo.subdivision,
            lastLogin: new Date(),
            photoURL: user.photoURL
        };
        
        await updateDoc(userDoc, userData).catch(async () => {
            // If document doesn't exist, create it
            await addDoc(collection(db, 'users'), userData);
        });
        
        console.log('✅ User info saved successfully');
    } catch (error) {
        console.error('❌ Error saving user info:', error);
        // Don't block login if this fails
    }
}

// Show login screen
function showLoginScreen() {
    document.getElementById('loginScreen').classList.remove('d-none');
    document.getElementById('mainApp').classList.add('d-none');
}

// Show main application
function showMainApp() {
    console.log('🚀 Showing main application...');
    document.getElementById('loginScreen').classList.add('d-none');
    document.getElementById('mainApp').classList.remove('d-none');
    
    // Update user info display
    document.getElementById('userInfo').innerHTML = `
        <img src="${currentUser.photoURL}" alt="Profile" class="rounded-circle me-2" width="32" height="32">
        Welcome, ${currentUser.displayName}
    `;
    
    setupNavigation();
    loadDashboard();
}

// Setup navigation based on user role
function setupNavigation() {
    console.log('🧭 Setting up navigation for role:', userRole);
    const sidebarNav = document.getElementById('sidebarNav');
    let navItems = [];
    
    navItems.push({
        id: 'dashboard',
        icon: 'fas fa-tachometer-alt',
        text: 'Dashboard',
        onclick: 'showSection("dashboard")'
    });
    
    navItems.push({
        id: 'newApplications',
        icon: 'fas fa-file-alt',
        text: 'New Applications',
        onclick: 'showSection("newApplications")'
    });
    
    navItems.push({
        id: 'workStatus',
        icon: 'fas fa-tools',
        text: 'Work Status',
        onclick: 'showSection("workStatus")'
    });
    
    if (userRole === 'xen') {
        navItems.push({
            id: 'userManagement',
            icon: 'fas fa-users-cog',
            text: 'Manage Users',
            onclick: 'showSection("userManagement")'
        });
    }
    
    sidebarNav.innerHTML = navItems.map(item => `
        <li class="nav-item">
            <a class="nav-link" href="#" id="nav-${item.id}" onclick="${item.onclick}">
                <i class="${item.icon}"></i>
                ${item.text}
            </a>
        </li>
    `).join('');
    
    document.getElementById('nav-dashboard').classList.add('active');
}

// Listen for authentication state changes
onAuthStateChanged(auth, (user) => {
    if (user) {
        console.log('👤 User is signed in:', user.email);
        checkUserAuthorization(user);
    } else {
        console.log('👤 User is signed out');
        showLoginScreen();
    }
});

// Export Firebase instances for use in other files
window.db = db;
window.auth = auth;
window.currentUser = currentUser;
window.userRole = userRole;
window.userSubdivision = userSubdivision;

console.log('🔥 Firebase configuration loaded successfully!');
