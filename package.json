{"name": "micada-division-monitoring", "version": "1.0.0", "description": "MICADA Division Monitoring System for tracking new applications and work progress", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["micada", "monitoring", "dashboard", "water-course"], "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "express": "^4.18.2", "express-session": "^1.17.3", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.0.1"}}