// Diagnostic script to test the exact issue

console.log('=== MICADA DIAGNOSTIC SCRIPT ===');

// Test 1: Check if basic JavaScript works
console.log('Test 1: Basic JavaScript execution - PASS');

// Test 2: Check if showSection function exists
console.log('Test 2: showSection function exists:', typeof showSection !== 'undefined');

// Test 3: Try to define a simple showSection function
function testShowSection(section) {
    console.log('Test showSection called with:', section);
    return true;
}

// Test 4: Make it globally accessible
window.testShowSection = testShowSection;
console.log('Test 4: Global function assignment - PASS');

// Test 5: Test onclick simulation
function simulateOnclick() {
    try {
        console.log('Test 5: Simulating onclick...');
        testShowSection('newApplications');
        console.log('Test 5: Onclick simulation - PASS');
    } catch (error) {
        console.log('Test 5: Onclick simulation - FAIL:', error.message);
    }
}

// Test 6: Check DOM readiness
document.addEventListener('DOMContentLoaded', function() {
    console.log('Test 6: DOM loaded - PASS');
    
    // Test 7: Check if main app elements exist
    const mainApp = document.getElementById('mainApp');
    console.log('Test 7: mainApp element exists:', !!mainApp);
    
    const sidebarNav = document.getElementById('sidebarNav');
    console.log('Test 7: sidebarNav element exists:', !!sidebarNav);
    
    const newAppsContent = document.getElementById('newApplicationsContent');
    console.log('Test 7: newApplicationsContent element exists:', !!newAppsContent);
    
    const workStatusContent = document.getElementById('workStatusContent');
    console.log('Test 7: workStatusContent element exists:', !!workStatusContent);
    
    // Test 8: Run onclick simulation after DOM is ready
    setTimeout(simulateOnclick, 1000);
});

console.log('=== DIAGNOSTIC SCRIPT LOADED ===');
