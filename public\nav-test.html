<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Navigation Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Navigation Test</h2>
        <p>Testing if navigation onclick handlers work</p>
        
        <div class="row">
            <div class="col-md-3">
                <h4>Navigation</h4>
                <ul class="nav flex-column" id="sidebarNav">
                    <!-- Navigation will be populated by JavaScript -->
                </ul>
            </div>
            
            <div class="col-md-9">
                <h4>Debug Output</h4>
                <div id="debugOutput" style="background: #f8f9fa; padding: 15px; height: 400px; overflow-y: auto; font-family: monospace; font-size: 12px;">
                    Debug output will appear here...<br>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Mock current user for testing
        let currentUser = { role: 'xen', name: 'Test User' };
        
        function log(message) {
            const output = document.getElementById('debugOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}<br>`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        function showSection(section) {
            log('showSection called with: ' + section);
            try {
                log('Function executed successfully for section: ' + section);
            } catch (error) {
                log('ERROR in showSection: ' + error.message);
            }
        }

        // Make function globally accessible
        window.showSection = showSection;

        function setupNavigation() {
            log('Setting up navigation...');
            
            const sidebarNav = document.getElementById('sidebarNav');
            let navItems = [];

            // Common navigation items
            navItems.push({
                id: 'dashboard',
                icon: 'fas fa-tachometer-alt',
                text: 'Dashboard',
                onclick: 'showSection("dashboard")'
            });

            navItems.push({
                id: 'newApplications',
                icon: 'fas fa-file-alt',
                text: 'New Applications',
                onclick: 'showSection("newApplications")'
            });

            navItems.push({
                id: 'workStatus',
                icon: 'fas fa-tools',
                text: 'Work Status',
                onclick: 'showSection("workStatus")'
            });

            // Xen-only navigation
            if (currentUser.role === 'xen') {
                navItems.push({
                    id: 'userManagement',
                    icon: 'fas fa-users-cog',
                    text: 'Manage Users',
                    onclick: 'showSection("userManagement")'
                });
            }

            // Build navigation HTML
            const navHTML = navItems.map(item => `
                <li class="nav-item">
                    <a class="nav-link" href="#" id="nav-${item.id}" onclick="${item.onclick}">
                        <i class="${item.icon}"></i>
                        ${item.text}
                    </a>
                </li>
            `).join('');
            
            log('Navigation HTML generated: ' + navHTML.length + ' characters');
            sidebarNav.innerHTML = navHTML;
            
            // Set dashboard as active
            const dashboardNav = document.getElementById('nav-dashboard');
            if (dashboardNav) {
                dashboardNav.classList.add('active');
                log('Dashboard set as active');
            } else {
                log('ERROR: Dashboard nav element not found');
            }
            
            log('Navigation setup completed');
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('DOM loaded');
            log('Bootstrap available: ' + (typeof bootstrap !== 'undefined'));
            log('showSection function available: ' + (typeof window.showSection !== 'undefined'));
            
            setupNavigation();
            
            // Test direct function call
            setTimeout(() => {
                log('Testing direct function call...');
                try {
                    showSection('test');
                    log('Direct function call successful');
                } catch (error) {
                    log('Direct function call failed: ' + error.message);
                }
            }, 1000);
        });
    </script>
</body>
</html>
