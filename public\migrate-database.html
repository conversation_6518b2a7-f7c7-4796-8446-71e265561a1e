<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Migration - MICADA</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>

<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h3>Database Migration Script</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h5><i class="fas fa-exclamation-triangle me-2"></i>Important</h5>
                            <p>This script will update the existing database structure to match the new requirements:
                            </p>
                            <ul>
                                <li>Update Sr. Numbers to simple format (1, 2, 3...)</li>
                                <li>Update Work Status table structure</li>
                                <li>Add Physical Progress field</li>
                                <li>Reorganize column order</li>
                            </ul>
                        </div>

                        <button id="migrateBtn" class="btn btn-primary btn-lg">Start Migration</button>
                        <button id="testBtn" class="btn btn-info">Test Connection</button>

                        <div id="migrationProgress" class="mt-4 d-none">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar"
                                    style="width: 0%"></div>
                            </div>
                            <div class="mt-2">
                                <small id="progressText" class="text-muted">Starting migration...</small>
                            </div>
                        </div>

                        <div id="migrationResults" class="mt-4 d-none">
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle me-2"></i>Migration Completed!</h5>
                                <p class="mb-0">Database has been successfully updated.</p>
                            </div>
                        </div>

                        <div id="migrationOutput" class="mt-4 p-3 bg-light"
                            style="height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;">
                            Migration output will appear here...<br>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, getDocs, doc, updateDoc, setDoc, addDoc } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyC4bwhHbZ9oc6mPkzIbQKJyIk9sSiA-S9s",
            authDomain: "micada-division.firebaseapp.com",
            projectId: "micada-division",
            storageBucket: "micada-division.appspot.com",
            messagingSenderId: "363841115848",
            appId: "1:363841115848:web:68ce295c4375e68fe077fd",
            measurementId: "G-1RXZT0K512"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        function log(message) {
            const output = document.getElementById('migrationOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}<br>`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        function updateProgress(percentage, text) {
            const progressBar = document.querySelector('.progress-bar');
            const progressText = document.getElementById('progressText');
            progressBar.style.width = percentage + '%';
            progressText.textContent = text;
        }

        // Test connection
        document.getElementById('testBtn').addEventListener('click', async () => {
            try {
                log('🔍 Testing database connection...');

                const appsSnapshot = await getDocs(collection(db, 'new_applications'));
                log(`✅ Applications collection: ${appsSnapshot.size} documents`);

                const worksSnapshot = await getDocs(collection(db, 'work_status'));
                log(`✅ Work status collection: ${worksSnapshot.size} documents`);

                log('✅ Database connection successful!');

            } catch (error) {
                log('❌ Database connection failed: ' + error.message);
            }
        });

        // Start migration
        document.getElementById('migrateBtn').addEventListener('click', async () => {
            const migrateBtn = document.getElementById('migrateBtn');
            const migrationProgress = document.getElementById('migrationProgress');
            const migrationResults = document.getElementById('migrationResults');

            migrateBtn.disabled = true;
            migrationProgress.classList.remove('d-none');
            migrationResults.classList.add('d-none');

            try {
                log('🚀 Starting database migration...');
                updateProgress(10, 'Connecting to database...');

                // Step 1: Migrate Applications Sr. Numbers
                log('📄 Migrating application Sr. Numbers...');
                updateProgress(25, 'Updating application Sr. Numbers...');
                await migrateApplicationSrNumbers();

                // Step 2: Migrate Work Status Structure
                log('🔧 Migrating work status structure...');
                updateProgress(50, 'Updating work status structure...');
                await migrateWorkStatusStructure();

                // Step 3: Add sample data with new structure
                log('➕ Adding sample data with new structure...');
                updateProgress(75, 'Adding sample data...');
                await addSampleDataWithNewStructure();

                updateProgress(100, 'Migration completed successfully!');

                setTimeout(() => {
                    migrationProgress.classList.add('d-none');
                    migrationResults.classList.remove('d-none');
                }, 1000);

                log('✅ Migration completed successfully!');

            } catch (error) {
                log('❌ Migration failed: ' + error.message);
                migrateBtn.disabled = false;
            }
        });

        // Migrate application Sr. Numbers and add new fields
        async function migrateApplicationSrNumbers() {
            const querySnapshot = await getDocs(collection(db, 'new_applications'));
            let count = 0;

            // Group by subdivision to reset Sr. Numbers per subdivision
            const subdivisionGroups = {};

            querySnapshot.docs.forEach(docSnapshot => {
                const data = docSnapshot.data();
                const subdivision = data.subdivision || 'unknown';

                if (!subdivisionGroups[subdivision]) {
                    subdivisionGroups[subdivision] = [];
                }
                subdivisionGroups[subdivision].push({ id: docSnapshot.id, data });
            });

            // Process each subdivision separately
            for (const [subdivision, docs] of Object.entries(subdivisionGroups)) {
                log(`📄 Processing ${subdivision} subdivision: ${docs.length} applications`);

                for (let i = 0; i < docs.length; i++) {
                    const { id, data } = docs[i];
                    const docRef = doc(db, 'new_applications', id);

                    // Create updated data with new fields
                    const updatedData = {
                        // Convert Sr. Number to simple format (1, 2, 3...)
                        sr_no: (i + 1).toString(),

                        // Existing fields
                        village: data.village || '',
                        constituency: data.constituency || '',
                        district: data.district || '',
                        rd: data.rd || '',
                        minor: data.minor || '',
                        work_type: data.work_type || '',
                        farmer_name: data.farmer_name || '',
                        contact: data.contact || '',
                        reference: data.reference || '',
                        subdivision: data.subdivision || subdivision,
                        createdAt: data.createdAt || new Date(),
                        createdBy: data.createdBy || 'migration',

                        // New fields with default values
                        status: data.status || 'In the norms',
                        reason_out_of_norms: data.reason_out_of_norms || '',
                        junior_engineer: data.junior_engineer || 'Not specified',
                        lastUpdated: data.lastUpdated || data.createdAt || new Date()
                    };

                    await updateDoc(docRef, updatedData);

                    count++;
                    log(`  ✓ Updated application ${id}: Sr.No ${updatedData.sr_no} (${subdivision})`);
                }
            }

            log(`✅ Updated ${count} applications with new structure`);
        }

        // Migrate work status structure
        async function migrateWorkStatusStructure() {
            const querySnapshot = await getDocs(collection(db, 'work_status'));
            let count = 0;

            for (const docSnapshot of querySnapshot.docs) {
                const data = docSnapshot.data();
                const docRef = doc(db, 'work_status', docSnapshot.id);

                // Create updated data with new structure
                const updatedData = {
                    // New primary fields
                    water_course_rd: data.water_course_rd || data.water_course || generateSampleRD(),
                    channel_name: data.channel_name || data.work_name || generateSampleChannelName(),
                    physical_progress: data.physical_progress || calculateProgress(data.length_lined, data.total_length_to_be_lined),
                    aa_no_date: data.aa_no_date || 'AA-001/2024 dt. 01.01.2024',

                    // Existing fields
                    type_of_work: data.type_of_work || 'Canal Lining',
                    total_length_to_be_lined: data.total_length_to_be_lined || '1000 RFT',
                    length_lined: data.length_lined || '500 RFT',
                    contractor: data.contractor || 'Not specified',
                    remarks: data.remarks || '',

                    // Location fields (moved to end)
                    village: data.village || '',
                    constituency: data.constituency || '',
                    district: data.district || '',

                    // New required fields
                    junior_engineer: data.junior_engineer || 'Not specified',

                    // System fields
                    subdivision: data.subdivision || 'unknown',
                    createdAt: data.createdAt || new Date(),
                    createdBy: data.createdBy || 'migration',
                    lastUpdated: data.lastUpdated || data.createdAt || new Date(),

                    // Keep old fields for backward compatibility
                    water_course: data.water_course,
                    work_name: data.work_name
                };

                await updateDoc(docRef, updatedData);

                count++;
                log(`  ✓ Updated work status ${docSnapshot.id}: ${updatedData.water_course_rd} - ${updatedData.channel_name}`);
            }

            log(`✅ Updated ${count} work status records with new structure`);
        }

        // Generate sample RD format
        function generateSampleRD() {
            const rdNumbers = ['1500', '2400', '3200', '4800', '5600', '124500', '89000'];
            const sides = ['L', 'R'];
            const randomRD = rdNumbers[Math.floor(Math.random() * rdNumbers.length)];
            const randomSide = sides[Math.floor(Math.random() * sides.length)];
            return `${randomRD}/${randomSide}`;
        }

        // Generate sample channel names
        function generateSampleChannelName() {
            const channels = [
                'Sudkain disty',
                'Narwana minor',
                'Hisar distributary',
                'Barwala minor',
                'Main canal',
                'Branch canal'
            ];
            return channels[Math.floor(Math.random() * channels.length)];
        }

        // Add sample data with new structure
        async function addSampleDataWithNewStructure() {
            log('📊 Adding sample applications with new structure...');

            const sampleApplications = [
                {
                    sr_no: '1',
                    village: 'Kaithal',
                    constituency: 'Kaithal',
                    district: 'Kaithal',
                    rd: '1500/L',
                    minor: 'Sudkain disty',
                    status: 'In the norms',
                    reason_out_of_norms: '',
                    work_type: 'Canal Lining',
                    farmer_name: 'Ram Singh',
                    contact: '9876543210',
                    reference: 'REF-001/2024',
                    junior_engineer: 'Er. Suresh Kumar',
                    subdivision: 'narwana',
                    createdAt: new Date(),
                    createdBy: 'migration',
                    lastUpdated: new Date()
                },
                {
                    sr_no: '2',
                    village: 'Narwana',
                    constituency: 'Narwana',
                    district: 'Kaithal',
                    rd: '2400/R',
                    minor: 'Narwana minor',
                    status: 'Out of norms',
                    reason_out_of_norms: 'Insufficient water flow in the channel',
                    work_type: 'Water Course Construction',
                    farmer_name: 'Krishan Lal',
                    contact: '9876543211',
                    reference: 'REF-002/2024',
                    junior_engineer: 'Er. Rajesh Sharma',
                    subdivision: 'narwana',
                    createdAt: new Date(),
                    createdBy: 'migration',
                    lastUpdated: new Date()
                },
                {
                    sr_no: '1',
                    village: 'Barwala',
                    constituency: 'Hisar',
                    district: 'Hisar',
                    rd: '124500/L',
                    minor: 'Barwala distributary',
                    status: 'In the norms',
                    reason_out_of_norms: '',
                    work_type: 'Canal Maintenance',
                    farmer_name: 'Jagdish Prasad',
                    contact: '9876543212',
                    reference: 'REF-003/2024',
                    junior_engineer: 'Er. Amit Singh',
                    subdivision: 'barwala',
                    createdAt: new Date(),
                    createdBy: 'migration',
                    lastUpdated: new Date()
                }
            ];

            for (const app of sampleApplications) {
                try {
                    await addDoc(collection(db, 'new_applications'), app);
                    log(`  ✓ Added sample application: ${app.village} (${app.subdivision})`);
                } catch (error) {
                    log(`  ❌ Error adding application: ${error.message}`);
                }
            }

            log('📊 Adding sample work status with new structure...');

            const sampleWorks = [
                {
                    water_course_rd: '1500/L',
                    channel_name: 'Sudkain disty',
                    physical_progress: 75,
                    aa_no_date: 'AA-001/2024 dt. 15.01.2024',
                    type_of_work: 'Canal Lining',
                    total_length_to_be_lined: '2000 RFT',
                    length_lined: '1500 RFT',
                    contractor: 'M/s ABC Construction',
                    junior_engineer: 'Er. Suresh Kumar',
                    remarks: 'Work progressing as per schedule',
                    village: 'Kaithal',
                    constituency: 'Kaithal',
                    district: 'Kaithal',
                    subdivision: 'narwana',
                    createdAt: new Date(),
                    createdBy: 'migration',
                    lastUpdated: new Date()
                },
                {
                    water_course_rd: '2400/R',
                    channel_name: 'Narwana minor',
                    physical_progress: 45,
                    aa_no_date: 'AA-002/2024 dt. 20.01.2024',
                    type_of_work: 'Water Course Construction',
                    total_length_to_be_lined: '1800 RFT',
                    length_lined: '810 RFT',
                    contractor: 'M/s XYZ Builders',
                    junior_engineer: 'Er. Rajesh Sharma',
                    remarks: 'Delayed due to weather conditions',
                    village: 'Narwana',
                    constituency: 'Narwana',
                    district: 'Kaithal',
                    subdivision: 'narwana',
                    createdAt: new Date(),
                    createdBy: 'migration',
                    lastUpdated: new Date()
                },
                {
                    water_course_rd: '124500/L',
                    channel_name: 'Barwala distributary',
                    physical_progress: 90,
                    aa_no_date: 'AA-003/2024 dt. 10.01.2024',
                    type_of_work: 'Canal Maintenance',
                    total_length_to_be_lined: '1200 RFT',
                    length_lined: '1080 RFT',
                    contractor: 'M/s PQR Infrastructure',
                    junior_engineer: 'Er. Amit Singh',
                    remarks: 'Near completion, quality checks ongoing',
                    village: 'Barwala',
                    constituency: 'Hisar',
                    district: 'Hisar',
                    subdivision: 'barwala',
                    createdAt: new Date(),
                    createdBy: 'migration',
                    lastUpdated: new Date()
                }
            ];

            for (const work of sampleWorks) {
                try {
                    await addDoc(collection(db, 'work_status'), work);
                    log(`  ✓ Added sample work: ${work.water_course_rd} - ${work.channel_name}`);
                } catch (error) {
                    log(`  ❌ Error adding work: ${error.message}`);
                }
            }

            log('✅ Sample data added with new structure');
        }

        // Calculate progress percentage from length data
        function calculateProgress(lengthLined, totalLength) {
            if (!lengthLined || !totalLength) return 0;

            const lined = parseFloat(lengthLined.toString().replace(/[^\d.]/g, ''));
            const total = parseFloat(totalLength.toString().replace(/[^\d.]/g, ''));

            if (total === 0) return 0;

            const progress = Math.round((lined / total) * 100);
            return Math.min(100, Math.max(0, progress));
        }

        log('🔥 Migration script loaded successfully');
        log('Click "Test Connection" to verify database access');
        log('Click "Start Migration" to update the database structure');
    </script>
</body>

</html>