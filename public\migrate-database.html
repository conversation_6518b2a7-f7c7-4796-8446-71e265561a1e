<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Migration - MICADA</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-warning text-dark">
                        <h3>Database Migration Script</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <h5><i class="fas fa-exclamation-triangle me-2"></i>Important</h5>
                            <p>This script will update the existing database structure to match the new requirements:</p>
                            <ul>
                                <li>Update Sr. Numbers to simple format (1, 2, 3...)</li>
                                <li>Update Work Status table structure</li>
                                <li>Add Physical Progress field</li>
                                <li>Reorganize column order</li>
                            </ul>
                        </div>
                        
                        <button id="migrateBtn" class="btn btn-primary btn-lg">Start Migration</button>
                        <button id="testBtn" class="btn btn-info">Test Connection</button>
                        
                        <div id="migrationProgress" class="mt-4 d-none">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="mt-2">
                                <small id="progressText" class="text-muted">Starting migration...</small>
                            </div>
                        </div>

                        <div id="migrationResults" class="mt-4 d-none">
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle me-2"></i>Migration Completed!</h5>
                                <p class="mb-0">Database has been successfully updated.</p>
                            </div>
                        </div>

                        <div id="migrationOutput" class="mt-4 p-3 bg-light" style="height: 300px; overflow-y: auto; font-family: monospace; font-size: 12px;">
                            Migration output will appear here...<br>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, getDocs, doc, updateDoc, setDoc } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyC4bwhHbZ9oc6mPkzIbQKJyIk9sSiA-S9s",
            authDomain: "micada-division.firebaseapp.com",
            projectId: "micada-division",
            storageBucket: "micada-division.appspot.com",
            messagingSenderId: "363841115848",
            appId: "1:363841115848:web:68ce295c4375e68fe077fd",
            measurementId: "G-1RXZT0K512"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        function log(message) {
            const output = document.getElementById('migrationOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}<br>`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        function updateProgress(percentage, text) {
            const progressBar = document.querySelector('.progress-bar');
            const progressText = document.getElementById('progressText');
            progressBar.style.width = percentage + '%';
            progressText.textContent = text;
        }

        // Test connection
        document.getElementById('testBtn').addEventListener('click', async () => {
            try {
                log('🔍 Testing database connection...');
                
                const appsSnapshot = await getDocs(collection(db, 'new_applications'));
                log(`✅ Applications collection: ${appsSnapshot.size} documents`);
                
                const worksSnapshot = await getDocs(collection(db, 'work_status'));
                log(`✅ Work status collection: ${worksSnapshot.size} documents`);
                
                log('✅ Database connection successful!');
                
            } catch (error) {
                log('❌ Database connection failed: ' + error.message);
            }
        });

        // Start migration
        document.getElementById('migrateBtn').addEventListener('click', async () => {
            const migrateBtn = document.getElementById('migrateBtn');
            const migrationProgress = document.getElementById('migrationProgress');
            const migrationResults = document.getElementById('migrationResults');
            
            migrateBtn.disabled = true;
            migrationProgress.classList.remove('d-none');
            migrationResults.classList.add('d-none');
            
            try {
                log('🚀 Starting database migration...');
                updateProgress(10, 'Connecting to database...');
                
                // Step 1: Migrate Applications Sr. Numbers
                log('📄 Migrating application Sr. Numbers...');
                updateProgress(25, 'Updating application Sr. Numbers...');
                await migrateApplicationSrNumbers();
                
                // Step 2: Migrate Work Status Structure
                log('🔧 Migrating work status structure...');
                updateProgress(50, 'Updating work status structure...');
                await migrateWorkStatusStructure();
                
                // Step 3: Add missing fields
                log('➕ Adding missing fields...');
                updateProgress(75, 'Adding missing fields...');
                await addMissingFields();
                
                updateProgress(100, 'Migration completed successfully!');
                
                setTimeout(() => {
                    migrationProgress.classList.add('d-none');
                    migrationResults.classList.remove('d-none');
                }, 1000);
                
                log('✅ Migration completed successfully!');
                
            } catch (error) {
                log('❌ Migration failed: ' + error.message);
                migrateBtn.disabled = false;
            }
        });

        // Migrate application Sr. Numbers
        async function migrateApplicationSrNumbers() {
            const querySnapshot = await getDocs(collection(db, 'new_applications'));
            let count = 0;
            
            for (const docSnapshot of querySnapshot.docs) {
                const data = docSnapshot.data();
                const docRef = doc(db, 'new_applications', docSnapshot.id);
                
                // Extract number from existing sr_no and convert to simple format
                let newSrNo = '1';
                if (data.sr_no) {
                    const numMatch = data.sr_no.toString().match(/(\d+)/);
                    if (numMatch) {
                        newSrNo = numMatch[1];
                    }
                }
                
                await updateDoc(docRef, {
                    sr_no: newSrNo
                });
                
                count++;
                log(`  ✓ Updated application ${docSnapshot.id}: ${data.sr_no} → ${newSrNo}`);
            }
            
            log(`✅ Updated ${count} application Sr. Numbers`);
        }

        // Migrate work status structure
        async function migrateWorkStatusStructure() {
            const querySnapshot = await getDocs(collection(db, 'work_status'));
            let count = 0;
            
            for (const docSnapshot of querySnapshot.docs) {
                const data = docSnapshot.data();
                const docRef = doc(db, 'work_status', docSnapshot.id);
                
                // Map old fields to new structure
                const updatedData = {
                    water_course_rd: data.water_course || data.water_course_rd || '',
                    channel_name: data.work_name || data.channel_name || '',
                    physical_progress: data.physical_progress || calculateProgress(data.length_lined, data.total_length_to_be_lined),
                    type_of_work: data.type_of_work || '',
                    total_length_to_be_lined: data.total_length_to_be_lined || '',
                    length_lined: data.length_lined || '',
                    contractor: data.contractor || '',
                    remarks: data.remarks || '',
                    village: data.village || '',
                    constituency: data.constituency || '',
                    district: data.district || '',
                    subdivision: data.subdivision || '',
                    createdAt: data.createdAt || new Date(),
                    createdBy: data.createdBy || 'migration',
                    // Keep old fields for backward compatibility
                    water_course: data.water_course,
                    work_name: data.work_name,
                    aa_no_date: data.aa_no_date
                };
                
                await updateDoc(docRef, updatedData);
                
                count++;
                log(`  ✓ Updated work status ${docSnapshot.id}`);
            }
            
            log(`✅ Updated ${count} work status records`);
        }

        // Add missing fields
        async function addMissingFields() {
            // Add any additional fields or corrections needed
            log('✅ All required fields are present');
        }

        // Calculate progress percentage from length data
        function calculateProgress(lengthLined, totalLength) {
            if (!lengthLined || !totalLength) return 0;
            
            const lined = parseFloat(lengthLined.toString().replace(/[^\d.]/g, ''));
            const total = parseFloat(totalLength.toString().replace(/[^\d.]/g, ''));
            
            if (total === 0) return 0;
            
            const progress = Math.round((lined / total) * 100);
            return Math.min(100, Math.max(0, progress));
        }

        log('🔥 Migration script loaded successfully');
        log('Click "Test Connection" to verify database access');
        log('Click "Start Migration" to update the database structure');
    </script>
</body>
</html>
