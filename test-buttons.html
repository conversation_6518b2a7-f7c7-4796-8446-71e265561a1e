<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Button Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Button Test Page</h2>
        <p>This page tests if the buttons work correctly.</p>
        
        <button class="btn btn-success me-2" id="testAddBtn">
            <i class="fas fa-plus me-1"></i>Test Add Button
        </button>
        
        <button class="btn btn-info me-2" onclick="alert('Onclick works!')">
            <i class="fas fa-download me-1"></i>Test Onclick
        </button>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('testAddBtn').addEventListener('click', function() {
            alert('Event listener works!');
        });
        
        console.log('Test page loaded');
    </script>
</body>
</html>
