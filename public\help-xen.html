<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XEN User Guide - MICADA Division</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .help-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
        }
        .feature-card {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .step-number {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        .icon-large {
            font-size: 3rem;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="help-section">
        <div class="container text-center">
            <i class="fas fa-user-tie icon-large"></i>
            <h1 class="display-4 fw-bold mb-3">XEN User Guide</h1>
            <p class="lead">Complete guide for Executive Engineers using the MICADA Division Monitoring System</p>
            <a href="index.html" class="btn btn-light btn-lg mt-3">
                <i class="fas fa-arrow-left me-2"></i>Back to Application
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container my-5">
        <!-- Overview -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card feature-card">
                    <div class="card-body text-center p-5">
                        <i class="fas fa-crown text-warning icon-large"></i>
                        <h2 class="card-title">XEN Privileges</h2>
                        <p class="card-text">As an Executive Engineer, you have complete access to all subdivision data, advanced reporting capabilities, and user management functions.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Key Features -->
        <div class="row mb-5">
            <div class="col-md-4 mb-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-eye text-primary icon-large"></i>
                        <h5 class="card-title">View All Data</h5>
                        <p class="card-text">Access applications and work status from all subdivisions (Narwana, Barwala, Hisar 1, Hisar 2)</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-file-excel text-success icon-large"></i>
                        <h5 class="card-title">Excel Reports</h5>
                        <p class="card-text">Generate comprehensive Excel reports with status filtering and adjusted serial numbers</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-pie text-info icon-large"></i>
                        <h5 class="card-title">Visual Analytics</h5>
                        <p class="card-text">View pie charts showing status distribution for applications and work progress</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- How to Use Applications -->
        <div class="card mb-5">
            <div class="card-header bg-primary text-white">
                <h3><i class="fas fa-file-alt me-2"></i>Managing Applications</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Viewing Applications</h5>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">1</div>
                            <div>Click on "New Applications" tab to view all applications</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">2</div>
                            <div>Use status filter to see "In the norms" or "Out of norms" cases</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">3</div>
                            <div>Use subdivision filter to focus on specific subdivision</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">4</div>
                            <div>Use date filter to see recent updates (1 day, week, month)</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5>Adding Applications</h5>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">1</div>
                            <div>Click "Add New Application" button</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">2</div>
                            <div>Select the subdivision first</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">3</div>
                            <div>Fill all required fields</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">4</div>
                            <div>If status is "Out of norms", provide reason</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- How to Use Work Status -->
        <div class="card mb-5">
            <div class="card-header bg-warning text-dark">
                <h3><i class="fas fa-tools me-2"></i>Managing Work Status</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Viewing Work Status</h5>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">1</div>
                            <div>Click on "Work Status" tab to view all work records</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">2</div>
                            <div>Use status filter: Ongoing, Completed, Delayed, On Hold</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">3</div>
                            <div>Monitor physical progress percentages</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">4</div>
                            <div>Check contractor performance and timelines</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5>Adding Work Records</h5>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">1</div>
                            <div>Click "Add New Work" button</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">2</div>
                            <div>Select subdivision and enter Water Course RD</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">3</div>
                            <div>Set work status and physical progress</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">4</div>
                            <div>Add contractor details and timeline</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Reports -->
        <div class="card mb-5">
            <div class="card-header bg-success text-white">
                <h3><i class="fas fa-chart-bar me-2"></i>Generating Reports</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Excel Reports</h5>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">1</div>
                            <div>Go to "Reports" tab</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">2</div>
                            <div>Select subdivision from dropdown</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">3</div>
                            <div>Optionally select status filter</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">4</div>
                            <div>Click "Generate Excel Report"</div>
                        </div>
                        <div class="alert alert-info">
                            <strong>Note:</strong> Reports have adjusted serial numbers starting from 1 for each subdivision
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5>Visual Analytics</h5>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">1</div>
                            <div>Pie charts show status distribution automatically</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">2</div>
                            <div>Applications chart: In norms vs Out of norms</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">3</div>
                            <div>Work chart: Ongoing, Completed, Delayed, On Hold</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">4</div>
                            <div>Numbers update automatically when data changes</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tips -->
        <div class="card">
            <div class="card-header bg-info text-white">
                <h3><i class="fas fa-lightbulb me-2"></i>Pro Tips</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Efficiency Tips</h5>
                        <ul class="list-unstyled">
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Use universal search to find specific records quickly</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Combine filters for precise data views</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Export filtered data for focused reports</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Monitor charts for quick status overview</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>Data Management</h5>
                        <ul class="list-unstyled">
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Edit any record by clicking the edit button</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Delete records with proper confirmation</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>All changes are tracked with timestamps</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Data is automatically backed up</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-4">
        <div class="container">
            <p>&copy; 2024 MICADA Division Monitoring System - XEN User Guide</p>
            <p>For technical support, contact the IT department</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
