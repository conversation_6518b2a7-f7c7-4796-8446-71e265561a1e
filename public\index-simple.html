<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MICADA Division Monitoring System</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="styles.css" rel="stylesheet">
</head>

<body>
    <!-- Login Screen -->
    <div id="loginScreen" class="d-flex align-items-center justify-content-center min-vh-100 bg-light">
        <div class="card shadow-lg" style="width: 400px;">
            <div class="card-body text-center p-5">
                <div class="mb-4">
                    <i class="fas fa-water text-primary" style="font-size: 3rem;"></i>
                    <h2 class="mt-3 text-primary">MICADA Division</h2>
                    <p class="text-muted">Monitoring System</p>
                </div>

                <button id="signInBtn" class="btn btn-danger btn-lg w-100">
                    <i class="fab fa-google me-2"></i>
                    Sign in with Google
                </button>

                <div class="mt-4">
                    <small class="text-muted">
                        Only authorized MICADA personnel can access this system
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="mainApp" class="d-none">
        <!-- Header -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-water me-2"></i>
                    MICADA Division Monitoring
                </a>

                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <span id="userInfo">User</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" id="signOutBtn">
                                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                                </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar -->
                <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                    <div class="position-sticky pt-3">
                        <ul class="nav flex-column" id="sidebarNav">
                            <!-- Navigation will be populated by JavaScript -->
                        </ul>
                    </div>
                </nav>

                <!-- Main Content -->
                <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                    <div
                        class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2" id="pageTitle">Dashboard</h1>
                    </div>

                    <!-- Dashboard Content -->
                    <div id="dashboardContent">
                        <div class="row">
                            <div class="col-md-6 col-lg-3 mb-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4 id="newAppsCount">0</h4>
                                                <p class="mb-0">New Applications</p>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-file-alt fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6 col-lg-3 mb-4">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4 id="activeWorksCount">0</h4>
                                                <p class="mb-0">Active Works</p>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-tools fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-success">
                            <h5><i class="fas fa-check-circle me-2"></i>System Status</h5>
                            <p class="mb-0">Firebase authentication and database are working correctly!</p>
                        </div>
                    </div>

                    <!-- New Applications Content -->
                    <div id="newApplicationsContent" class="d-none">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3>New Water Course Applications</h3>
                            <div>
                                <button class="btn btn-primary" onclick="showAddApplicationModal()">
                                    <i class="fas fa-plus me-1"></i>Add Application
                                </button>
                                <button class="btn btn-success" onclick="downloadTemplate('applications')">
                                    <i class="fas fa-download me-1"></i>Download Template
                                </button>
                            </div>
                        </div>

                        <!-- Search Container for Xen -->
                        <div id="xenSearchContainer" class="d-none mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <input type="text" class="form-control" id="universalSearch"
                                        placeholder="Search across all fields..."
                                        onkeyup="handleUniversalSearch(event)">
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr id="applicationsTableHeader">
                                        <!-- Headers will be populated by JavaScript -->
                                    </tr>
                                </thead>
                                <tbody id="applicationsTableBody">
                                    <!-- Data will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Work Status Content -->
                    <div id="workStatusContent" class="d-none">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3>Work Status Monitoring</h3>
                            <div>
                                <button class="btn btn-primary" onclick="showAddWorkModal()">
                                    <i class="fas fa-plus me-1"></i>Add Work
                                </button>
                                <button class="btn btn-success" onclick="downloadTemplate('works')">
                                    <i class="fas fa-download me-1"></i>Download Template
                                </button>
                            </div>
                        </div>

                        <!-- Search Container for Xen -->
                        <div id="xenSearchContainer2" class="d-none mb-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <input type="text" class="form-control" id="universalSearchWorks"
                                        placeholder="Search across all fields..."
                                        onkeyup="handleUniversalSearchWorks(event)">
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr id="workStatusTableHeader">
                                        <!-- Headers will be populated by JavaScript -->
                                    </tr>
                                </thead>
                                <tbody id="workStatusTableBody">
                                    <!-- Data will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- User Management Content -->
                    <div id="userManagementContent" class="d-none">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h3>User Management</h3>
                            <button class="btn btn-primary" onclick="showAddUserModal()">
                                <i class="fas fa-plus me-1"></i>Add New User
                            </button>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Subdivision</th>
                                        <th>Email</th>
                                        <th>Last Login</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="usersTableBody">
                                    <!-- Data will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>

    <!-- Add Application Modal -->
    <div class="modal fade" id="applicationModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="applicationModalTitle">Add New Application</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="applicationForm">
                        <input type="hidden" id="applicationId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="srNo" class="form-label">Sr. No</label>
                                <input type="text" class="form-control" id="srNo" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="village" class="form-label">Village</label>
                                <input type="text" class="form-control" id="village" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="constituency" class="form-label">Constituency</label>
                                <input type="text" class="form-control" id="constituency" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="district" class="form-label">District</label>
                                <input type="text" class="form-control" id="district" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="rd" class="form-label">RD</label>
                                <input type="text" class="form-control" id="rd" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="minor" class="form-label">Minor</label>
                                <input type="text" class="form-control" id="minor" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select class="form-select" id="status" required>
                                    <option value="">Select Status</option>
                                    <option value="Pending Review">Pending Review</option>
                                    <option value="Under Review">Under Review</option>
                                    <option value="Approved">Approved</option>
                                    <option value="In Progress">In Progress</option>
                                    <option value="Completed">Completed</option>
                                    <option value="Rejected">Rejected</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="workType" class="form-label">Work Type</label>
                                <input type="text" class="form-control" id="workType" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="farmerName" class="form-label">Farmer Name</label>
                                <input type="text" class="form-control" id="farmerName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="contact" class="form-label">Contact</label>
                                <input type="text" class="form-control" id="contact" required>
                            </div>
                            <div class="col-md-12 mb-3">
                                <label for="reference" class="form-label">Reference</label>
                                <input type="text" class="form-control" id="reference" required>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveApplication()">Save Application</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Work Modal -->
    <div class="modal fade" id="workModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="workModalTitle">Add New Work</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="workForm">
                        <input type="hidden" id="workId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="workVillage" class="form-label">Village</label>
                                <input type="text" class="form-control" id="workVillage" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="workConstituency" class="form-label">Constituency</label>
                                <input type="text" class="form-control" id="workConstituency" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="workDistrict" class="form-label">District</label>
                                <input type="text" class="form-control" id="workDistrict" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="waterCourse" class="form-label">Water Course</label>
                                <input type="text" class="form-control" id="waterCourse" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="workName" class="form-label">Work Name</label>
                                <input type="text" class="form-control" id="workName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="aaNoDate" class="form-label">AA No. & Date</label>
                                <input type="text" class="form-control" id="aaNoDate" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="typeOfWork" class="form-label">Type of Work</label>
                                <input type="text" class="form-control" id="typeOfWork" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="totalLength" class="form-label">Total Length to be Lined</label>
                                <input type="text" class="form-control" id="totalLength" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="lengthLined" class="form-label">Length Lined</label>
                                <input type="text" class="form-control" id="lengthLined" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="contractor" class="form-label">Contractor</label>
                                <input type="text" class="form-control" id="contractor" required>
                            </div>
                            <div class="col-md-12 mb-3">
                                <label for="remarks" class="form-label">Remarks</label>
                                <textarea class="form-control" id="remarks" rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveWork()">Save Work</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add User Modal -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm">
                        <div class="mb-3">
                            <label for="userEmail" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="userEmail" required>
                            <div class="form-text">User must have a Google account with this email</div>
                        </div>
                        <div class="mb-3">
                            <label for="userSubdivision" class="form-label">Subdivision</label>
                            <select class="form-select" id="userSubdivision" required>
                                <option value="">Select Subdivision</option>
                                <option value="narwana">MICAD Sub Division, Narwana</option>
                                <option value="barwala">Barwala CAD Subdivision</option>
                                <option value="hisar1">CAD Subdivision 1 Hisar</option>
                                <option value="hisar2">CAD Subdivision 2 Hisar</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="userName" class="form-label">Full Name</label>
                            <input type="text" class="form-control" id="userName" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="saveNewUser()">Add User</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getAuth, GoogleAuthProvider, signInWithPopup, signOut, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js";
        import { getFirestore, collection, getDocs, doc, setDoc, addDoc, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyC4bwhHbZ9oc6mPkzIbQKJyIk9sSiA-S9s",
            authDomain: "micada-division.firebaseapp.com",
            projectId: "micada-division",
            storageBucket: "micada-division.appspot.com",
            messagingSenderId: "363841115848",
            appId: "1:363841115848:web:68ce295c4375e68fe077fd",
            measurementId: "G-1RXZT0K512"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);
        const provider = new GoogleAuthProvider();

        // Global variables
        let currentUser = null;
        let userRole = null;
        let userSubdivision = null;

        // Sign in function
        async function signInWithGoogle() {
            try {
                console.log('🔐 Starting Google Sign-in...');
                const result = await signInWithPopup(auth, provider);
                const user = result.user;
                console.log('✅ User signed in:', user.email);

                await checkUserAuthorization(user);

            } catch (error) {
                console.error('❌ Sign in error:', error);
                alert('Sign in failed: ' + error.message);
            }
        }

        // Check user authorization
        async function checkUserAuthorization(user) {
            const email = user.email;
            console.log('🔍 Checking authorization for:', email);

            try {
                // Check if user is XEN
                if (email === '<EMAIL>') {
                    console.log('✅ XEN user detected');
                    currentUser = user;
                    userRole = 'xen';
                    userSubdivision = null;

                    await saveUserInfo(user, { role: 'xen', subdivision: null, name: 'Executive Engineer MICADA' });
                    showMainApp();
                    return;
                }

                // Check subdivision users
                const authorizedUsersQuery = query(
                    collection(db, 'authorized_users'),
                    where('email', '==', email),
                    where('active', '==', true)
                );
                const querySnapshot = await getDocs(authorizedUsersQuery);

                if (!querySnapshot.empty) {
                    const userDoc = querySnapshot.docs[0];
                    const userData = userDoc.data();

                    currentUser = user;
                    userRole = userData.role;
                    userSubdivision = userData.subdivision;

                    await saveUserInfo(user, userData);
                    showMainApp();
                } else {
                    alert(`Access Denied!\n\nEmail: ${email}\n\nYou are not authorized to access this system.\nPlease contact the MICADA Executive Engineer to get access.`);
                    await signOut(auth);
                }
            } catch (error) {
                console.error('❌ Error checking authorization:', error);
                alert('Error checking user authorization: ' + error.message);
                await signOut(auth);
            }
        }

        // Save user info
        async function saveUserInfo(user, userInfo) {
            try {
                const userData = {
                    uid: user.uid,
                    email: user.email,
                    name: user.displayName || userInfo.name || 'Unknown User',
                    role: userInfo.role,
                    subdivision: userInfo.subdivision,
                    lastLogin: new Date(),
                    photoURL: user.photoURL || null
                };

                const userDoc = doc(db, 'users', user.uid);
                await setDoc(userDoc, userData, { merge: true });
                console.log('✅ User info saved');
            } catch (error) {
                console.error('❌ Error saving user info:', error);
            }
        }

        // Show login screen
        function showLoginScreen() {
            document.getElementById('loginScreen').classList.remove('d-none');
            document.getElementById('mainApp').classList.add('d-none');
        }

        // Show main application
        function showMainApp() {
            console.log('🚀 Showing main application...');
            document.getElementById('loginScreen').classList.add('d-none');
            document.getElementById('mainApp').classList.remove('d-none');

            // Update user info display
            document.getElementById('userInfo').innerHTML = `
                ${currentUser.photoURL ? `<img src="${currentUser.photoURL}" class="rounded-circle me-2" width="24" height="24">` : ''}
                Welcome, ${currentUser.displayName}
            `;

            setupNavigation();
            loadDashboard();
        }

        // Setup navigation
        function setupNavigation() {
            const sidebarNav = document.getElementById('sidebarNav');
            let navItems = [
                { id: 'dashboard', icon: 'fas fa-tachometer-alt', text: 'Dashboard' },
                { id: 'newApplications', icon: 'fas fa-file-alt', text: 'New Applications' },
                { id: 'workStatus', icon: 'fas fa-tools', text: 'Work Status' }
            ];

            if (userRole === 'xen') {
                navItems.push({ id: 'userManagement', icon: 'fas fa-users-cog', text: 'Manage Users' });
            }

            sidebarNav.innerHTML = navItems.map(item => `
                <li class="nav-item">
                    <a class="nav-link" href="#" id="nav-${item.id}" onclick="showSection('${item.id}')">
                        <i class="${item.icon}"></i>
                        ${item.text}
                    </a>
                </li>
            `).join('');

            document.getElementById('nav-dashboard').classList.add('active');
        }

        // Show section
        window.showSection = function (section) {
            // Hide all sections
            ['dashboardContent', 'newApplicationsContent', 'workStatusContent', 'userManagementContent'].forEach(s => {
                document.getElementById(s).classList.add('d-none');
            });

            // Remove active from all nav
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            // Show selected section
            document.getElementById(section + 'Content').classList.remove('d-none');
            document.getElementById('nav-' + section).classList.add('active');

            // Update page title
            const titles = {
                dashboard: 'Dashboard',
                newApplications: 'New Water Course Applications',
                workStatus: 'Work Status Monitoring',
                userManagement: 'User Management'
            };
            document.getElementById('pageTitle').textContent = titles[section];

            // Load section-specific data
            if (section === 'newApplications') {
                loadNewApplications();
            } else if (section === 'workStatus') {
                loadWorkStatus();
            } else if (section === 'userManagement') {
                loadUserManagement();
            }
        };

        // Load new applications
        async function loadNewApplications() {
            console.log('Loading new applications...');
            try {
                const applicationsQuery = userRole === 'xen'
                    ? query(collection(db, 'new_applications'), orderBy('createdAt', 'desc'))
                    : query(collection(db, 'new_applications'),
                        where('subdivision', '==', userSubdivision),
                        orderBy('createdAt', 'desc'));

                const querySnapshot = await getDocs(applicationsQuery);
                const applications = [];

                querySnapshot.forEach((doc) => {
                    applications.push({ id: doc.id, ...doc.data() });
                });

                console.log('Applications loaded:', applications.length);
                displayApplicationsTable(applications);

                // Show search for Xen users
                if (userRole === 'xen') {
                    document.getElementById('xenSearchContainer').classList.remove('d-none');
                }

            } catch (error) {
                console.error('Error loading applications:', error);
                alert('Error loading applications: ' + error.message);
            }
        }

        // Load work status
        async function loadWorkStatus() {
            console.log('Loading work status...');
            try {
                const worksQuery = userRole === 'xen'
                    ? query(collection(db, 'work_status'), orderBy('createdAt', 'desc'))
                    : query(collection(db, 'work_status'),
                        where('subdivision', '==', userSubdivision),
                        orderBy('createdAt', 'desc'));

                const querySnapshot = await getDocs(worksQuery);
                const works = [];

                querySnapshot.forEach((doc) => {
                    works.push({ id: doc.id, ...doc.data() });
                });

                console.log('Works loaded:', works.length);
                displayWorkStatusTable(works);

                // Show search for Xen users
                if (userRole === 'xen') {
                    document.getElementById('xenSearchContainer2').classList.remove('d-none');
                }

            } catch (error) {
                console.error('Error loading work status:', error);
                alert('Error loading work status: ' + error.message);
            }
        }

        // Load user management
        async function loadUserManagement() {
            if (userRole !== 'xen') return;

            try {
                const [usersSnapshot, authorizedSnapshot] = await Promise.all([
                    getDocs(collection(db, 'users')),
                    getDocs(collection(db, 'authorized_users'))
                ]);

                const loggedInUsers = [];
                const authorizedUsers = [];

                usersSnapshot.forEach((doc) => {
                    loggedInUsers.push({ id: doc.id, ...doc.data() });
                });

                authorizedSnapshot.forEach((doc) => {
                    authorizedUsers.push({ id: doc.id, ...doc.data() });
                });

                displayUsersTable(loggedInUsers, authorizedUsers);

            } catch (error) {
                console.error('Error loading users:', error);
            }
        }

        // Load dashboard
        async function loadDashboard() {
            try {
                const [appsSnapshot, worksSnapshot] = await Promise.all([
                    getDocs(collection(db, 'new_applications')),
                    getDocs(collection(db, 'work_status'))
                ]);

                document.getElementById('newAppsCount').textContent = appsSnapshot.size;
                document.getElementById('activeWorksCount').textContent = worksSnapshot.size;
            } catch (error) {
                console.error('Error loading dashboard:', error);
            }
        }

        // Display applications table
        function displayApplicationsTable(applications) {
            const headerRow = document.getElementById('applicationsTableHeader');
            const tbody = document.getElementById('applicationsTableBody');

            // Define headers
            let headers = ['Sr. No', 'Village', 'Constituency', 'District', 'RD', 'Minor', 'Status', 'Work Type', 'Farmer Name', 'Contact', 'Reference'];

            if (userRole === 'xen') {
                headers.push('Subdivision');
            }

            // Build header
            headerRow.innerHTML = headers.map(header => `<th>${header}</th>`).join('');

            // Build table body
            tbody.innerHTML = applications.map(app => {
                let row = `
                    <tr>
                        <td>${app.sr_no || ''}</td>
                        <td>${app.village || ''}</td>
                        <td>${app.constituency || ''}</td>
                        <td>${app.district || ''}</td>
                        <td>${app.rd || ''}</td>
                        <td>${app.minor || ''}</td>
                        <td><span class="badge ${getStatusBadgeClass(app.status)}">${app.status || ''}</span></td>
                        <td>${app.work_type || ''}</td>
                        <td>${app.farmer_name || ''}</td>
                        <td>${app.contact || ''}</td>
                        <td>${app.reference || ''}</td>
                `;

                if (userRole === 'xen') {
                    row += `<td>${app.subdivision || ''}</td>`;
                }

                row += '</tr>';
                return row;
            }).join('');
        }

        // Display work status table
        function displayWorkStatusTable(works) {
            const headerRow = document.getElementById('workStatusTableHeader');
            const tbody = document.getElementById('workStatusTableBody');

            // Define headers
            let headers = ['Village', 'Constituency', 'District', 'Water Course', 'Work Name', 'AA No. & Date', 'Type of Work', 'Total Length', 'Length Lined', 'Contractor', 'Remarks'];

            if (userRole === 'xen') {
                headers.push('Subdivision');
            }

            // Build header
            headerRow.innerHTML = headers.map(header => `<th>${header}</th>`).join('');

            // Build table body
            tbody.innerHTML = works.map(work => {
                let row = `
                    <tr>
                        <td>${work.village || ''}</td>
                        <td>${work.constituency || ''}</td>
                        <td>${work.district || ''}</td>
                        <td>${work.water_course || ''}</td>
                        <td>${work.work_name || ''}</td>
                        <td>${work.aa_no_date || ''}</td>
                        <td>${work.type_of_work || ''}</td>
                        <td>${work.total_length_to_be_lined || ''}</td>
                        <td>${work.length_lined || ''}</td>
                        <td>${work.contractor || ''}</td>
                        <td>${work.remarks || ''}</td>
                `;

                if (userRole === 'xen') {
                    row += `<td>${work.subdivision || ''}</td>`;
                }

                row += '</tr>';
                return row;
            }).join('');
        }

        // Display users table
        function displayUsersTable(loggedInUsers, authorizedUsers) {
            const tbody = document.getElementById('usersTableBody');

            // Combine and organize user data
            const userMap = new Map();

            // Add authorized users first
            authorizedUsers.forEach(authUser => {
                userMap.set(authUser.email, {
                    email: authUser.email,
                    subdivision: authUser.subdivision,
                    role: authUser.role,
                    name: authUser.name,
                    active: authUser.active,
                    lastLogin: null,
                    hasLoggedIn: false
                });
            });

            // Update with login information
            loggedInUsers.forEach(loggedUser => {
                if (userMap.has(loggedUser.email)) {
                    const existing = userMap.get(loggedUser.email);
                    existing.lastLogin = loggedUser.lastLogin;
                    existing.hasLoggedIn = true;
                    existing.photoURL = loggedUser.photoURL;
                }
            });

            tbody.innerHTML = Array.from(userMap.values()).map(user => `
                <tr>
                    <td>${user.subdivision || 'XEN Office'}</td>
                    <td>
                        ${user.photoURL ? `<img src="${user.photoURL}" class="rounded-circle me-2" width="24" height="24">` : ''}
                        ${user.email}
                    </td>
                    <td>${user.lastLogin ? new Date(user.lastLogin.seconds * 1000).toLocaleDateString() : 'Never'}</td>
                    <td>
                        <span class="badge ${user.active ? 'bg-success' : 'bg-danger'}">
                            ${user.active ? 'Active' : 'Inactive'}
                        </span>
                        ${user.hasLoggedIn ? '<span class="badge bg-info ms-1">Logged In</span>' : ''}
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary me-1" onclick="editUserAccess('${user.email}')">
                            <i class="fas fa-edit"></i> Edit
                        </button>
                        <button class="btn btn-sm ${user.active ? 'btn-outline-danger' : 'btn-outline-success'}"
                                onclick="toggleUserAccess('${user.email}', ${!user.active})">
                            <i class="fas ${user.active ? 'fa-ban' : 'fa-check'}"></i>
                            ${user.active ? 'Disable' : 'Enable'}
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // Get status badge class
        function getStatusBadgeClass(status) {
            if (!status) return 'bg-secondary';
            const statusLower = status.toLowerCase();
            if (statusLower.includes('approved') || statusLower.includes('completed')) return 'bg-success';
            if (statusLower.includes('rejected')) return 'bg-danger';
            if (statusLower.includes('progress')) return 'bg-warning';
            return 'bg-primary';
        }

        // Modal functions
        window.showAddApplicationModal = function () {
            document.getElementById('applicationForm').reset();
            document.getElementById('applicationId').value = '';
            document.getElementById('applicationModalTitle').textContent = 'Add New Application';
            const modal = new bootstrap.Modal(document.getElementById('applicationModal'));
            modal.show();
        };

        window.showAddWorkModal = function () {
            document.getElementById('workForm').reset();
            document.getElementById('workId').value = '';
            document.getElementById('workModalTitle').textContent = 'Add New Work';
            const modal = new bootstrap.Modal(document.getElementById('workModal'));
            modal.show();
        };

        window.showAddUserModal = function () {
            if (userRole !== 'xen') {
                alert('Access denied. Only XEN can manage users.');
                return;
            }
            document.getElementById('addUserForm').reset();
            const modal = new bootstrap.Modal(document.getElementById('addUserModal'));
            modal.show();
        };

        // Save functions
        window.saveApplication = async function () {
            const formData = {
                sr_no: document.getElementById('srNo').value,
                village: document.getElementById('village').value,
                constituency: document.getElementById('constituency').value,
                district: document.getElementById('district').value,
                rd: document.getElementById('rd').value,
                minor: document.getElementById('minor').value,
                status: document.getElementById('status').value,
                work_type: document.getElementById('workType').value,
                farmer_name: document.getElementById('farmerName').value,
                contact: document.getElementById('contact').value,
                reference: document.getElementById('reference').value,
                subdivision: userSubdivision || 'xen',
                createdAt: new Date(),
                createdBy: currentUser.email
            };

            try {
                await addDoc(collection(db, 'new_applications'), formData);
                alert('Application saved successfully!');
                const modal = bootstrap.Modal.getInstance(document.getElementById('applicationModal'));
                modal.hide();
                loadNewApplications();
            } catch (error) {
                console.error('Error saving application:', error);
                alert('Error saving application: ' + error.message);
            }
        };

        window.saveWork = async function () {
            const formData = {
                village: document.getElementById('workVillage').value,
                constituency: document.getElementById('workConstituency').value,
                district: document.getElementById('workDistrict').value,
                water_course: document.getElementById('waterCourse').value,
                work_name: document.getElementById('workName').value,
                aa_no_date: document.getElementById('aaNoDate').value,
                type_of_work: document.getElementById('typeOfWork').value,
                total_length_to_be_lined: document.getElementById('totalLength').value,
                length_lined: document.getElementById('lengthLined').value,
                contractor: document.getElementById('contractor').value,
                remarks: document.getElementById('remarks').value,
                subdivision: userSubdivision || 'xen',
                createdAt: new Date(),
                createdBy: currentUser.email
            };

            try {
                await addDoc(collection(db, 'work_status'), formData);
                alert('Work saved successfully!');
                const modal = bootstrap.Modal.getInstance(document.getElementById('workModal'));
                modal.hide();
                loadWorkStatus();
            } catch (error) {
                console.error('Error saving work:', error);
                alert('Error saving work: ' + error.message);
            }
        };

        window.saveNewUser = async function () {
            if (userRole !== 'xen') {
                alert('Access denied. Only XEN can manage users.');
                return;
            }

            const email = document.getElementById('userEmail').value;
            const subdivision = document.getElementById('userSubdivision').value;
            const name = document.getElementById('userName').value;

            if (!email || !subdivision || !name) {
                alert('Please fill all fields');
                return;
            }

            try {
                const existingQuery = query(collection(db, 'authorized_users'), where('email', '==', email));
                const existingSnapshot = await getDocs(existingQuery);

                if (!existingSnapshot.empty) {
                    alert('User with this email already exists');
                    return;
                }

                const newUser = {
                    email: email,
                    role: 'subdivision',
                    subdivision: subdivision,
                    name: name,
                    active: true,
                    createdAt: new Date(),
                    createdBy: '<EMAIL>'
                };

                await addDoc(collection(db, 'authorized_users'), newUser);
                alert('User added successfully!');

                const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
                modal.hide();
                loadUserManagement();

            } catch (error) {
                console.error('Error adding user:', error);
                alert('Error adding user: ' + error.message);
            }
        };

        // Utility functions
        window.downloadTemplate = function (type) {
            let headers, filename;

            if (type === 'applications') {
                headers = ['Sr. No', 'Village', 'Constituency', 'District', 'RD', 'Minor', 'Status', 'Work Type', 'Farmer Name', 'Contact', 'Reference'];
                filename = 'new_applications_template.csv';
            } else if (type === 'works') {
                headers = ['Village', 'Constituency', 'District', 'Water Course', 'Work Name', 'AA No. & Date', 'Type of Work', 'Total Length', 'Length Lined', 'Contractor', 'Remarks'];
                filename = 'work_status_template.csv';
            }

            const csvContent = headers.join(',') + '\n';
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        };

        window.handleUniversalSearch = function (event) {
            const searchTerm = event.target.value.toLowerCase();
            const tableRows = document.querySelectorAll('#applicationsTableBody tr');

            tableRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        };

        window.handleUniversalSearchWorks = function (event) {
            const searchTerm = event.target.value.toLowerCase();
            const tableRows = document.querySelectorAll('#workStatusTableBody tr');

            tableRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        };

        window.editUserAccess = function (email) {
            alert('Edit user functionality - Email: ' + email);
        };

        window.toggleUserAccess = function (email, newStatus) {
            alert('Toggle user access functionality - Email: ' + email + ', New Status: ' + newStatus);
        };

        // Event listeners
        document.getElementById('signInBtn').addEventListener('click', signInWithGoogle);
        document.getElementById('signOutBtn').addEventListener('click', async () => {
            await signOut(auth);
        });

        // Auth state listener
        onAuthStateChanged(auth, (user) => {
            if (user) {
                console.log('👤 User signed in:', user.email);
                checkUserAuthorization(user);
            } else {
                console.log('👤 User signed out');
                showLoginScreen();
            }
        });

        console.log('🔥 Simple Firebase app loaded successfully');
    </script>
</body>

</html>