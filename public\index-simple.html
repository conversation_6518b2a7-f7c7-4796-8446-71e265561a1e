<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MICADA Division Monitoring System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="styles.css" rel="stylesheet">
</head>
<body>
    <!-- Login Screen -->
    <div id="loginScreen" class="d-flex align-items-center justify-content-center min-vh-100 bg-light">
        <div class="card shadow-lg" style="width: 400px;">
            <div class="card-body text-center p-5">
                <div class="mb-4">
                    <i class="fas fa-water text-primary" style="font-size: 3rem;"></i>
                    <h2 class="mt-3 text-primary">MICADA Division</h2>
                    <p class="text-muted">Monitoring System</p>
                </div>
                
                <button id="signInBtn" class="btn btn-danger btn-lg w-100">
                    <i class="fab fa-google me-2"></i>
                    Sign in with Google
                </button>
                
                <div class="mt-4">
                    <small class="text-muted">
                        Only authorized MICADA personnel can access this system
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="mainApp" class="d-none">
        <!-- Header -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
            <div class="container-fluid">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-water me-2"></i>
                    MICADA Division Monitoring
                </a>
                
                <div class="navbar-nav ms-auto">
                    <div class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <span id="userInfo">User</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" id="signOutBtn">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </nav>

        <div class="container-fluid">
            <div class="row">
                <!-- Sidebar -->
                <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar collapse">
                    <div class="position-sticky pt-3">
                        <ul class="nav flex-column" id="sidebarNav">
                            <!-- Navigation will be populated by JavaScript -->
                        </ul>
                    </div>
                </nav>

                <!-- Main Content -->
                <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                    <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                        <h1 class="h2" id="pageTitle">Dashboard</h1>
                    </div>

                    <!-- Dashboard Content -->
                    <div id="dashboardContent">
                        <div class="row">
                            <div class="col-md-6 col-lg-3 mb-4">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4 id="newAppsCount">0</h4>
                                                <p class="mb-0">New Applications</p>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-file-alt fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6 col-lg-3 mb-4">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4 id="activeWorksCount">0</h4>
                                                <p class="mb-0">Active Works</p>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-tools fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-success">
                            <h5><i class="fas fa-check-circle me-2"></i>System Status</h5>
                            <p class="mb-0">Firebase authentication and database are working correctly!</p>
                        </div>
                    </div>

                    <!-- New Applications Content -->
                    <div id="newApplicationsContent" class="d-none">
                        <h3>New Water Course Applications</h3>
                        <div class="alert alert-info">
                            <p class="mb-0">Application management functionality will be loaded here.</p>
                        </div>
                    </div>

                    <!-- Work Status Content -->
                    <div id="workStatusContent" class="d-none">
                        <h3>Work Status Monitoring</h3>
                        <div class="alert alert-info">
                            <p class="mb-0">Work status management functionality will be loaded here.</p>
                        </div>
                    </div>

                    <!-- User Management Content -->
                    <div id="userManagementContent" class="d-none">
                        <h3>User Management</h3>
                        <div class="alert alert-info">
                            <p class="mb-0">User management functionality will be loaded here.</p>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getAuth, GoogleAuthProvider, signInWithPopup, signOut, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js";
        import { getFirestore, collection, getDocs, doc, setDoc, query, where } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyC4bwhHbZ9oc6mPkzIbQKJyIk9sSiA-S9s",
            authDomain: "micada-division.firebaseapp.com",
            projectId: "micada-division",
            storageBucket: "micada-division.appspot.com",
            messagingSenderId: "363841115848",
            appId: "1:363841115848:web:68ce295c4375e68fe077fd",
            measurementId: "G-1RXZT0K512"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);
        const provider = new GoogleAuthProvider();

        // Global variables
        let currentUser = null;
        let userRole = null;
        let userSubdivision = null;

        // Sign in function
        async function signInWithGoogle() {
            try {
                console.log('🔐 Starting Google Sign-in...');
                const result = await signInWithPopup(auth, provider);
                const user = result.user;
                console.log('✅ User signed in:', user.email);
                
                await checkUserAuthorization(user);
                
            } catch (error) {
                console.error('❌ Sign in error:', error);
                alert('Sign in failed: ' + error.message);
            }
        }

        // Check user authorization
        async function checkUserAuthorization(user) {
            const email = user.email;
            console.log('🔍 Checking authorization for:', email);
            
            try {
                // Check if user is XEN
                if (email === '<EMAIL>') {
                    console.log('✅ XEN user detected');
                    currentUser = user;
                    userRole = 'xen';
                    userSubdivision = null;
                    
                    await saveUserInfo(user, { role: 'xen', subdivision: null, name: 'Executive Engineer MICADA' });
                    showMainApp();
                    return;
                }
                
                // Check subdivision users
                const authorizedUsersQuery = query(
                    collection(db, 'authorized_users'), 
                    where('email', '==', email), 
                    where('active', '==', true)
                );
                const querySnapshot = await getDocs(authorizedUsersQuery);
                
                if (!querySnapshot.empty) {
                    const userDoc = querySnapshot.docs[0];
                    const userData = userDoc.data();
                    
                    currentUser = user;
                    userRole = userData.role;
                    userSubdivision = userData.subdivision;
                    
                    await saveUserInfo(user, userData);
                    showMainApp();
                } else {
                    alert(`Access Denied!\n\nEmail: ${email}\n\nYou are not authorized to access this system.\nPlease contact the MICADA Executive Engineer to get access.`);
                    await signOut(auth);
                }
            } catch (error) {
                console.error('❌ Error checking authorization:', error);
                alert('Error checking user authorization: ' + error.message);
                await signOut(auth);
            }
        }

        // Save user info
        async function saveUserInfo(user, userInfo) {
            try {
                const userData = {
                    uid: user.uid,
                    email: user.email,
                    name: user.displayName || userInfo.name || 'Unknown User',
                    role: userInfo.role,
                    subdivision: userInfo.subdivision,
                    lastLogin: new Date(),
                    photoURL: user.photoURL || null
                };
                
                const userDoc = doc(db, 'users', user.uid);
                await setDoc(userDoc, userData, { merge: true });
                console.log('✅ User info saved');
            } catch (error) {
                console.error('❌ Error saving user info:', error);
            }
        }

        // Show login screen
        function showLoginScreen() {
            document.getElementById('loginScreen').classList.remove('d-none');
            document.getElementById('mainApp').classList.add('d-none');
        }

        // Show main application
        function showMainApp() {
            console.log('🚀 Showing main application...');
            document.getElementById('loginScreen').classList.add('d-none');
            document.getElementById('mainApp').classList.remove('d-none');
            
            // Update user info display
            document.getElementById('userInfo').innerHTML = `
                ${currentUser.photoURL ? `<img src="${currentUser.photoURL}" class="rounded-circle me-2" width="24" height="24">` : ''}
                Welcome, ${currentUser.displayName}
            `;
            
            setupNavigation();
            loadDashboard();
        }

        // Setup navigation
        function setupNavigation() {
            const sidebarNav = document.getElementById('sidebarNav');
            let navItems = [
                { id: 'dashboard', icon: 'fas fa-tachometer-alt', text: 'Dashboard' },
                { id: 'newApplications', icon: 'fas fa-file-alt', text: 'New Applications' },
                { id: 'workStatus', icon: 'fas fa-tools', text: 'Work Status' }
            ];
            
            if (userRole === 'xen') {
                navItems.push({ id: 'userManagement', icon: 'fas fa-users-cog', text: 'Manage Users' });
            }
            
            sidebarNav.innerHTML = navItems.map(item => `
                <li class="nav-item">
                    <a class="nav-link" href="#" id="nav-${item.id}" onclick="showSection('${item.id}')">
                        <i class="${item.icon}"></i>
                        ${item.text}
                    </a>
                </li>
            `).join('');
            
            document.getElementById('nav-dashboard').classList.add('active');
        }

        // Show section
        window.showSection = function(section) {
            // Hide all sections
            ['dashboardContent', 'newApplicationsContent', 'workStatusContent', 'userManagementContent'].forEach(s => {
                document.getElementById(s).classList.add('d-none');
            });
            
            // Remove active from all nav
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            
            // Show selected section
            document.getElementById(section + 'Content').classList.remove('d-none');
            document.getElementById('nav-' + section).classList.add('active');
            
            // Update page title
            const titles = {
                dashboard: 'Dashboard',
                newApplications: 'New Water Course Applications',
                workStatus: 'Work Status Monitoring',
                userManagement: 'User Management'
            };
            document.getElementById('pageTitle').textContent = titles[section];
        };

        // Load dashboard
        async function loadDashboard() {
            try {
                const [appsSnapshot, worksSnapshot] = await Promise.all([
                    getDocs(collection(db, 'new_applications')),
                    getDocs(collection(db, 'work_status'))
                ]);
                
                document.getElementById('newAppsCount').textContent = appsSnapshot.size;
                document.getElementById('activeWorksCount').textContent = worksSnapshot.size;
            } catch (error) {
                console.error('Error loading dashboard:', error);
            }
        }

        // Event listeners
        document.getElementById('signInBtn').addEventListener('click', signInWithGoogle);
        document.getElementById('signOutBtn').addEventListener('click', async () => {
            await signOut(auth);
        });

        // Auth state listener
        onAuthStateChanged(auth, (user) => {
            if (user) {
                console.log('👤 User signed in:', user.email);
                checkUserAuthorization(user);
            } else {
                console.log('👤 User signed out');
                showLoginScreen();
            }
        });

        console.log('🔥 Simple Firebase app loaded successfully');
    </script>
</body>
</html>
