<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SDO User Guide - MICADA Division</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .help-section {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 60px 0;
        }
        .feature-card {
            transition: transform 0.3s ease;
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .step-number {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        .icon-large {
            font-size: 3rem;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="help-section">
        <div class="container text-center">
            <i class="fas fa-user-cog icon-large"></i>
            <h1 class="display-4 fw-bold mb-3">SDO User Guide</h1>
            <p class="lead">Complete guide for Sub Divisional Officers using the MICADA Division Monitoring System</p>
            <a href="index.html" class="btn btn-light btn-lg mt-3">
                <i class="fas fa-arrow-left me-2"></i>Back to Application
            </a>
        </div>
    </div>

    <!-- Main Content -->
    <div class="container my-5">
        <!-- Overview -->
        <div class="row mb-5">
            <div class="col-12">
                <div class="card feature-card">
                    <div class="card-body text-center p-5">
                        <i class="fas fa-shield-alt text-success icon-large"></i>
                        <h2 class="card-title">SDO Access</h2>
                        <p class="card-text">As a Sub Divisional Officer, you can manage applications and work status for your specific subdivision with full edit and delete capabilities.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Key Features -->
        <div class="row mb-5">
            <div class="col-md-4 mb-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-database text-primary icon-large"></i>
                        <h5 class="card-title">Your Data Only</h5>
                        <p class="card-text">Access and manage applications and work status only for your subdivision</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-edit text-warning icon-large"></i>
                        <h5 class="card-title">Full Edit Control</h5>
                        <p class="card-text">Add, edit, and delete records with automatic serial number generation</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="card feature-card h-100">
                    <div class="card-body text-center">
                        <i class="fas fa-chart-pie text-info icon-large"></i>
                        <h5 class="card-title">Status Analytics</h5>
                        <p class="card-text">View pie charts showing status distribution for your subdivision data</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- How to Use Applications -->
        <div class="card mb-5">
            <div class="card-header bg-primary text-white">
                <h3><i class="fas fa-file-alt me-2"></i>Managing Applications</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Adding New Applications</h5>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">1</div>
                            <div>Click "Add New Application" button</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">2</div>
                            <div>Fill in village, constituency, and district details</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">3</div>
                            <div>Enter RD (like 1500/L) and minor details</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">4</div>
                            <div>Select status: "In the norms" or "Out of norms"</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">5</div>
                            <div>If "Out of norms", provide reason</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">6</div>
                            <div>Add farmer details and contact information</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5>Managing Existing Applications</h5>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">1</div>
                            <div>Use search box to find specific applications</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">2</div>
                            <div>Filter by status to see "In norms" or "Out of norms"</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">3</div>
                            <div>Click edit button to modify any record</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">4</div>
                            <div>Click delete button to remove records</div>
                        </div>
                        <div class="alert alert-info">
                            <strong>Note:</strong> Serial numbers are automatically generated and managed
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- How to Use Work Status -->
        <div class="card mb-5">
            <div class="card-header bg-warning text-dark">
                <h3><i class="fas fa-tools me-2"></i>Managing Work Status</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Adding Work Records</h5>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">1</div>
                            <div>Click "Add New Work" button</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">2</div>
                            <div>Enter Water Course RD (e.g., 1500/L, 2400/R)</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">3</div>
                            <div>Add channel name (e.g., Sudkain disty)</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">4</div>
                            <div>Set physical progress percentage (0-100%)</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">5</div>
                            <div>Select work status: Ongoing, Completed, Delayed, On Hold</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">6</div>
                            <div>Add contractor and timeline details</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h5>Monitoring Progress</h5>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">1</div>
                            <div>View all work records in the table</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">2</div>
                            <div>Filter by work status to focus on specific types</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">3</div>
                            <div>Update progress percentages regularly</div>
                        </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="step-number">4</div>
                            <div>Change status as work progresses</div>
                        </div>
                        <div class="alert alert-success">
                            <strong>Tip:</strong> Keep progress updated for accurate reporting
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Entry Guidelines -->
        <div class="card mb-5">
            <div class="card-header bg-info text-white">
                <h3><i class="fas fa-clipboard-check me-2"></i>Data Entry Guidelines</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>RD Format Examples</h5>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item"><strong>1500/L</strong> - 1500 meters, Left side</li>
                            <li class="list-group-item"><strong>2400/R</strong> - 2400 meters, Right side</li>
                            <li class="list-group-item"><strong>124500/L</strong> - 124.5 km, Left side</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>Channel Name Examples</h5>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item"><strong>Sudkain disty</strong></li>
                            <li class="list-group-item"><strong>Narwana minor</strong></li>
                            <li class="list-group-item"><strong>Barwala distributary</strong></li>
                        </ul>
                    </div>
                </div>
                <div class="row mt-4">
                    <div class="col-md-6">
                        <h5>Application Status</h5>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex align-items-center">
                                <span class="badge bg-success me-2">✓</span>
                                <strong>In the norms</strong> - Application meets all criteria
                            </li>
                            <li class="list-group-item d-flex align-items-center">
                                <span class="badge bg-danger me-2">✗</span>
                                <strong>Out of norms</strong> - Requires reason explanation
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>Work Status Options</h5>
                        <ul class="list-group list-group-flush">
                            <li class="list-group-item d-flex align-items-center">
                                <span class="badge bg-primary me-2">●</span>
                                <strong>Ongoing</strong> - Work in progress
                            </li>
                            <li class="list-group-item d-flex align-items-center">
                                <span class="badge bg-success me-2">●</span>
                                <strong>Completed</strong> - Work finished
                            </li>
                            <li class="list-group-item d-flex align-items-center">
                                <span class="badge bg-danger me-2">●</span>
                                <strong>Delayed</strong> - Behind schedule
                            </li>
                            <li class="list-group-item d-flex align-items-center">
                                <span class="badge bg-warning me-2">●</span>
                                <strong>On Hold</strong> - Temporarily stopped
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tips -->
        <div class="card">
            <div class="card-header bg-success text-white">
                <h3><i class="fas fa-lightbulb me-2"></i>Best Practices</h3>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Data Quality</h5>
                        <ul class="list-unstyled">
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Double-check all entries before saving</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Use consistent formatting for RD and channel names</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Provide clear reasons for "Out of norms" cases</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Update work progress regularly</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>Efficiency Tips</h5>
                        <ul class="list-unstyled">
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Use search to quickly find specific records</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Filter by status to focus on specific cases</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Monitor pie charts for quick overview</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Keep contact information up to date</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-dark text-white text-center py-4">
        <div class="container">
            <p>&copy; 2024 MICADA Division Monitoring System - SDO User Guide</p>
            <p>For technical support, contact the IT department</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
