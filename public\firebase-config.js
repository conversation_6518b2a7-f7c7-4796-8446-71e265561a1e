// Firebase Configuration
import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
import { getAuth, GoogleAuthProvider, signInWithPopup, signOut, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js";
import { getFirestore, collection, addDoc, getDocs, doc, updateDoc, deleteDoc, setDoc, query, where, orderBy } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

// Your Firebase configuration - Updated with correct project details
const firebaseConfig = {
    apiKey: "AIzaSyC4bwhHbZ9oc6mPkzIbQKJyIk9sSiA-S9s",
    authDomain: "micada-division.firebaseapp.com",
    projectId: "micada-division",
    storageBucket: "micada-division.appspot.com",
    messagingSenderId: "363841115848",
    appId: "1:363841115848:web:68ce295c4375e68fe077fd",
    measurementId: "G-1RXZT0K512"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const db = getFirestore(app);
const provider = new GoogleAuthProvider();

// Global variables
let currentUser = null;
let userRole = null;
let userSubdivision = null;

// Authentication functions
window.signInWithGoogle = async function () {
    try {
        const result = await signInWithPopup(auth, provider);
        const user = result.user;
        console.log('User signed in:', user.email);

        // Check if user is authorized
        await checkUserAuthorization(user);

    } catch (error) {
        console.error('Sign in error:', error);
        alert('Sign in failed: ' + error.message);
    }
};

window.signOutUser = async function () {
    try {
        await signOut(auth);
        console.log('User signed out');
        showLoginScreen();
    } catch (error) {
        console.error('Sign out error:', error);
    }
};

// Check user authorization
async function checkUserAuthorization(user) {
    const email = user.email;
    console.log('🔍 Checking authorization for:', email);

    try {
        // First check if user is XEN
        if (email === '<EMAIL>') {
            console.log('✅ XEN user detected');
            currentUser = user;
            userRole = 'xen';
            userSubdivision = null;

            // Save XEN user info
            await saveUserInfo(user, { role: 'xen', subdivision: null, name: 'Executive Engineer MICADA' });

            // Update global variables
            window.currentUser = currentUser;
            window.userRole = userRole;
            window.userSubdivision = userSubdivision;

            showMainApp();
            return;
        }

        // Check if user is authorized subdivision user by looking in Firestore
        console.log('🔍 Checking subdivision user authorization...');
        const authorizedUsersQuery = query(
            collection(db, 'authorized_users'),
            where('email', '==', email),
            where('active', '==', true)
        );
        const querySnapshot = await getDocs(authorizedUsersQuery);

        if (!querySnapshot.empty) {
            console.log('✅ Authorized subdivision user found');
            const userDoc = querySnapshot.docs[0];
            const userData = userDoc.data();

            currentUser = user;
            userRole = userData.role;
            userSubdivision = userData.subdivision;

            // Update global variables
            window.currentUser = currentUser;
            window.userRole = userRole;
            window.userSubdivision = userSubdivision;

            await saveUserInfo(user, userData);
            showMainApp();
        } else {
            console.log('❌ User not authorized:', email);
            alert(`Access Denied!\n\nEmail: ${email}\n\nYou are not authorized to access this system.\nPlease contact the MICADA Executive Engineer (<EMAIL>) to get access.`);
            await signOut(auth);
        }
    } catch (error) {
        console.error('❌ Error checking authorization:', error);
        console.error('Error details:', error.message);
        alert('Error checking user authorization: ' + error.message + '\n\nPlease try again or contact support.');
        await signOut(auth);
    }
}

// Save user info to Firestore
async function saveUserInfo(user, userInfo) {
    try {
        console.log('💾 Saving user info to Firestore...');

        const userData = {
            uid: user.uid,
            email: user.email,
            name: user.displayName || userInfo.name || 'Unknown User',
            role: userInfo.role,
            subdivision: userInfo.subdivision,
            lastLogin: new Date(),
            photoURL: user.photoURL || null
        };

        console.log('User data to save:', userData);

        // Use setDoc with merge to create or update
        const userDoc = doc(db, 'users', user.uid);
        await setDoc(userDoc, userData, { merge: true });

        console.log('✅ User info saved successfully');
    } catch (error) {
        console.error('❌ Error saving user info:', error);
        console.error('Error details:', error.message);
        // Don't block login if this fails
    }
}

// Show login screen
function showLoginScreen() {
    document.getElementById('loginScreen').classList.remove('d-none');
    document.getElementById('mainApp').classList.add('d-none');
}

// Show main application
function showMainApp() {
    document.getElementById('loginScreen').classList.add('d-none');
    document.getElementById('mainApp').classList.remove('d-none');

    // Update user info display
    document.getElementById('userInfo').innerHTML = `
        <img src="${currentUser.photoURL}" alt="Profile" class="rounded-circle me-2" width="32" height="32">
        Welcome, ${currentUser.displayName}
    `;

    setupNavigation();
    loadDashboard();
}

// Setup navigation based on user role
function setupNavigation() {
    const sidebarNav = document.getElementById('sidebarNav');
    let navItems = [];

    navItems.push({
        id: 'dashboard',
        icon: 'fas fa-tachometer-alt',
        text: 'Dashboard',
        onclick: 'showSection("dashboard")'
    });

    navItems.push({
        id: 'newApplications',
        icon: 'fas fa-file-alt',
        text: 'New Applications',
        onclick: 'showSection("newApplications")'
    });

    navItems.push({
        id: 'workStatus',
        icon: 'fas fa-tools',
        text: 'Work Status',
        onclick: 'showSection("workStatus")'
    });

    if (userRole === 'xen') {
        navItems.push({
            id: 'userManagement',
            icon: 'fas fa-users-cog',
            text: 'Manage Users',
            onclick: 'showSection("userManagement")'
        });
    }

    sidebarNav.innerHTML = navItems.map(item => `
        <li class="nav-item">
            <a class="nav-link" href="#" id="nav-${item.id}" onclick="${item.onclick}">
                <i class="${item.icon}"></i>
                ${item.text}
            </a>
        </li>
    `).join('');

    document.getElementById('nav-dashboard').classList.add('active');
}

// Listen for authentication state changes
onAuthStateChanged(auth, (user) => {
    if (user) {
        console.log('User is signed in:', user.email);
        // Add a small delay to ensure everything is loaded
        setTimeout(() => {
            checkUserAuthorization(user);
        }, 500);
    } else {
        console.log('User is signed out');
        showLoginScreen();
    }
});

// Export Firebase instances for use in other files
window.db = db;
window.auth = auth;
window.currentUser = currentUser;
window.userRole = userRole;
window.userSubdivision = userSubdivision;
