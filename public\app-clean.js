// MICADA Division Monitoring System - Clean Version

// Global variables
let currentUser = null;
let authToken = null;
let currentSection = 'dashboard';

// Define functions first
function showAddApplicationModal() {
    console.log('showAddApplicationModal called');
    try {
        const modalElement = document.getElementById('applicationModal');
        if (!modalElement) {
            alert('Modal not found. Please refresh the page.');
            return;
        }
        
        document.getElementById('applicationModalTitle').textContent = 'Add New Application';
        document.getElementById('applicationForm').reset();
        document.getElementById('applicationId').value = '';
        
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
        console.log('Modal shown successfully');
    } catch (error) {
        console.error('Error showing modal:', error);
        alert('Error opening form: ' + error.message);
    }
}

function showAddWorkModal() {
    console.log('showAddWorkModal called');
    try {
        document.getElementById('workModalTitle').textContent = 'Add New Work';
        document.getElementById('workForm').reset();
        document.getElementById('workId').value = '';
        
        const modal = new bootstrap.Modal(document.getElementById('workModal'));
        modal.show();
        console.log('Work modal shown');
    } catch (error) {
        console.error('Error showing work modal:', error);
        alert('Error opening form: ' + error.message);
    }
}

function downloadTemplate(type) {
    console.log('downloadTemplate called with type:', type);
    let headers, filename;
    
    if (type === 'applications') {
        headers = ['Sr. No', 'Village', 'Constituency', 'District', 'RD', 'MINOR', 'Status', 'Work Type', "Farmer's Name", 'Contact', 'Reference'];
        filename = 'new_applications_template.csv';
    } else if (type === 'works') {
        headers = ['Village', 'Constituency', 'District', 'Water Course Name', 'Name of Work', 'AA No. & Date', 'Type of Work', 'Total Length to be Lined', 'Length Lined', 'Contractor', 'Remarks'];
        filename = 'work_status_template.csv';
    }
    
    const csvContent = headers.join(',') + '\n';
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

function showUploadModal(type) {
    console.log('showUploadModal called with type:', type);
    document.getElementById('uploadType').value = type;
    document.getElementById('uploadModalTitle').textContent = 
        type === 'applications' ? 'Bulk Upload Applications' : 'Bulk Upload Work Status';
    document.getElementById('uploadFile').value = '';
    
    const modal = new bootstrap.Modal(document.getElementById('uploadModal'));
    modal.show();
}

function logout() {
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    location.reload();
}

function showSection(section) {
    console.log('showSection called with:', section);
    
    // Hide all content sections
    const sections = ['dashboardContent', 'newApplicationsContent', 'workStatusContent', 'userManagementContent'];
    sections.forEach(s => {
        const element = document.getElementById(s);
        if (element) element.classList.add('d-none');
    });
    
    // Remove active class from all nav items
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    // Show selected section and set nav active
    currentSection = section;
    const navElement = document.getElementById('nav-' + section);
    if (navElement) navElement.classList.add('active');
    
    switch(section) {
        case 'dashboard':
            document.getElementById('dashboardContent').classList.remove('d-none');
            document.getElementById('pageTitle').textContent = 'Dashboard';
            loadDashboard();
            break;
        case 'newApplications':
            document.getElementById('newApplicationsContent').classList.remove('d-none');
            document.getElementById('pageTitle').textContent = 'New Water Course Applications';
            loadNewApplications();
            break;
        case 'workStatus':
            document.getElementById('workStatusContent').classList.remove('d-none');
            document.getElementById('pageTitle').textContent = 'Work Status Monitoring';
            loadWorkStatus();
            break;
        case 'userManagement':
            if (currentUser && currentUser.role === 'xen') {
                document.getElementById('userManagementContent').classList.remove('d-none');
                document.getElementById('pageTitle').textContent = 'User Management';
                loadUserManagement();
            }
            break;
    }
}

// Make functions globally accessible
window.showAddApplicationModal = showAddApplicationModal;
window.showAddWorkModal = showAddWorkModal;
window.downloadTemplate = downloadTemplate;
window.showUploadModal = showUploadModal;
window.logout = logout;
window.showSection = showSection;

console.log('Functions defined and made global');
