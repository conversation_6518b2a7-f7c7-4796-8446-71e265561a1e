<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Database - MICADA</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-danger text-white">
                        <h3>⚠️ Database Reset Script</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-danger">
                            <h5><i class="fas fa-exclamation-triangle me-2"></i>DANGER ZONE</h5>
                            <p><strong>This will completely reset the database and create fresh data with the new structure:</strong></p>
                            <ul>
                                <li>Delete ALL existing applications and work status records</li>
                                <li>Create new sample data with updated structure</li>
                                <li>Reset Sr. Numbers to start from 1 for each subdivision</li>
                                <li>Add all new required fields</li>
                            </ul>
                            <p class="mb-0"><strong>This action cannot be undone!</strong></p>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="confirmReset">
                                <label class="form-check-label" for="confirmReset">
                                    I understand this will delete all existing data
                                </label>
                            </div>
                        </div>
                        
                        <button id="resetBtn" class="btn btn-danger btn-lg" disabled>Reset Database</button>
                        <button id="testBtn" class="btn btn-info">Test Connection</button>
                        
                        <div id="resetProgress" class="mt-4 d-none">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated bg-danger" 
                                     role="progressbar" style="width: 0%"></div>
                            </div>
                            <div class="mt-2">
                                <small id="progressText" class="text-muted">Starting reset...</small>
                            </div>
                        </div>

                        <div id="resetResults" class="mt-4 d-none">
                            <div class="alert alert-success">
                                <h5><i class="fas fa-check-circle me-2"></i>Database Reset Completed!</h5>
                                <p class="mb-0">Database has been reset with new structure and sample data.</p>
                            </div>
                        </div>

                        <div id="resetOutput" class="mt-4 p-3 bg-light" style="height: 400px; overflow-y: auto; font-family: monospace; font-size: 12px;">
                            Reset output will appear here...<br>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js";
        import { getFirestore, collection, getDocs, doc, deleteDoc, addDoc } from "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyC4bwhHbZ9oc6mPkzIbQKJyIk9sSiA-S9s",
            authDomain: "micada-division.firebaseapp.com",
            projectId: "micada-division",
            storageBucket: "micada-division.appspot.com",
            messagingSenderId: "363841115848",
            appId: "1:363841115848:web:68ce295c4375e68fe077fd",
            measurementId: "G-1RXZT0K512"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);

        function log(message) {
            const output = document.getElementById('resetOutput');
            const timestamp = new Date().toLocaleTimeString();
            output.innerHTML += `[${timestamp}] ${message}<br>`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        function updateProgress(percentage, text) {
            const progressBar = document.querySelector('.progress-bar');
            const progressText = document.getElementById('progressText');
            progressBar.style.width = percentage + '%';
            progressText.textContent = text;
        }

        // Enable reset button when checkbox is checked
        document.getElementById('confirmReset').addEventListener('change', function() {
            document.getElementById('resetBtn').disabled = !this.checked;
        });

        // Test connection
        document.getElementById('testBtn').addEventListener('click', async () => {
            try {
                log('🔍 Testing database connection...');
                
                const appsSnapshot = await getDocs(collection(db, 'new_applications'));
                log(`✅ Applications collection: ${appsSnapshot.size} documents`);
                
                const worksSnapshot = await getDocs(collection(db, 'work_status'));
                log(`✅ Work status collection: ${worksSnapshot.size} documents`);
                
                log('✅ Database connection successful!');
                
            } catch (error) {
                log('❌ Database connection failed: ' + error.message);
            }
        });

        // Start reset
        document.getElementById('resetBtn').addEventListener('click', async () => {
            const resetBtn = document.getElementById('resetBtn');
            const resetProgress = document.getElementById('resetProgress');
            const resetResults = document.getElementById('resetResults');
            
            resetBtn.disabled = true;
            resetProgress.classList.remove('d-none');
            resetResults.classList.add('d-none');
            
            try {
                log('🚀 Starting database reset...');
                updateProgress(10, 'Connecting to database...');
                
                // Step 1: Delete existing data
                log('🗑️ Deleting existing applications...');
                updateProgress(25, 'Deleting existing applications...');
                await deleteAllApplications();
                
                log('🗑️ Deleting existing work status...');
                updateProgress(40, 'Deleting existing work status...');
                await deleteAllWorkStatus();
                
                // Step 2: Create new data with updated structure
                log('📊 Creating new applications with updated structure...');
                updateProgress(60, 'Creating new applications...');
                await createNewApplications();
                
                log('📊 Creating new work status with updated structure...');
                updateProgress(80, 'Creating new work status...');
                await createNewWorkStatus();
                
                updateProgress(100, 'Reset completed successfully!');
                
                setTimeout(() => {
                    resetProgress.classList.add('d-none');
                    resetResults.classList.remove('d-none');
                }, 1000);
                
                log('✅ Database reset completed successfully!');
                
            } catch (error) {
                log('❌ Reset failed: ' + error.message);
                resetBtn.disabled = false;
            }
        });

        // Delete all applications
        async function deleteAllApplications() {
            const querySnapshot = await getDocs(collection(db, 'new_applications'));
            let count = 0;
            
            for (const docSnapshot of querySnapshot.docs) {
                await deleteDoc(doc(db, 'new_applications', docSnapshot.id));
                count++;
                log(`  ✓ Deleted application ${docSnapshot.id}`);
            }
            
            log(`✅ Deleted ${count} applications`);
        }

        // Delete all work status
        async function deleteAllWorkStatus() {
            const querySnapshot = await getDocs(collection(db, 'work_status'));
            let count = 0;
            
            for (const docSnapshot of querySnapshot.docs) {
                await deleteDoc(doc(db, 'work_status', docSnapshot.id));
                count++;
                log(`  ✓ Deleted work status ${docSnapshot.id}`);
            }
            
            log(`✅ Deleted ${count} work status records`);
        }

        // Create new applications with updated structure
        async function createNewApplications() {
            const newApplications = [
                {
                    sr_no: '1',
                    village: 'Kaithal',
                    constituency: 'Kaithal',
                    district: 'Kaithal',
                    rd: '1500/L',
                    minor: 'Sudkain disty',
                    status: 'In the norms',
                    reason_out_of_norms: '',
                    work_type: 'Canal Lining',
                    farmer_name: 'Ram Singh',
                    contact: '9876543210',
                    reference: 'REF-001/2024',
                    junior_engineer: 'Er. Suresh Kumar',
                    subdivision: 'narwana',
                    createdAt: new Date(),
                    createdBy: 'system',
                    lastUpdated: new Date()
                },
                {
                    sr_no: '2',
                    village: 'Narwana',
                    constituency: 'Narwana', 
                    district: 'Kaithal',
                    rd: '2400/R',
                    minor: 'Narwana minor',
                    status: 'Out of norms',
                    reason_out_of_norms: 'Insufficient water flow in the channel',
                    work_type: 'Water Course Construction',
                    farmer_name: 'Krishan Lal',
                    contact: '9876543211',
                    reference: 'REF-002/2024',
                    junior_engineer: 'Er. Rajesh Sharma',
                    subdivision: 'narwana',
                    createdAt: new Date(),
                    createdBy: 'system',
                    lastUpdated: new Date()
                },
                {
                    sr_no: '1',
                    village: 'Barwala',
                    constituency: 'Hisar',
                    district: 'Hisar',
                    rd: '124500/L',
                    minor: 'Barwala distributary',
                    status: 'In the norms',
                    reason_out_of_norms: '',
                    work_type: 'Canal Maintenance',
                    farmer_name: 'Jagdish Prasad',
                    contact: '9876543212',
                    reference: 'REF-003/2024',
                    junior_engineer: 'Er. Amit Singh',
                    subdivision: 'barwala',
                    createdAt: new Date(),
                    createdBy: 'system',
                    lastUpdated: new Date()
                },
                {
                    sr_no: '1',
                    village: 'Hisar',
                    constituency: 'Hisar',
                    district: 'Hisar',
                    rd: '3200/R',
                    minor: 'Hisar distributary',
                    status: 'In the norms',
                    reason_out_of_norms: '',
                    work_type: 'Water Course Repair',
                    farmer_name: 'Mohan Lal',
                    contact: '9876543213',
                    reference: 'REF-004/2024',
                    junior_engineer: 'Er. Vikash Kumar',
                    subdivision: 'hisar1',
                    createdAt: new Date(),
                    createdBy: 'system',
                    lastUpdated: new Date()
                }
            ];

            for (const app of newApplications) {
                await addDoc(collection(db, 'new_applications'), app);
                log(`  ✓ Created application: ${app.village} - ${app.rd} (${app.subdivision})`);
            }

            log(`✅ Created ${newApplications.length} new applications`);
        }

        // Create new work status with updated structure
        async function createNewWorkStatus() {
            const newWorkStatus = [
                {
                    water_course_rd: '1500/L',
                    channel_name: 'Sudkain disty',
                    physical_progress: 75,
                    aa_no_date: 'AA-001/2024 dt. 15.01.2024',
                    type_of_work: 'Canal Lining',
                    total_length_to_be_lined: '2000 RFT',
                    length_lined: '1500 RFT',
                    contractor: 'M/s ABC Construction',
                    junior_engineer: 'Er. Suresh Kumar',
                    remarks: 'Work progressing as per schedule',
                    village: 'Kaithal',
                    constituency: 'Kaithal',
                    district: 'Kaithal',
                    subdivision: 'narwana',
                    createdAt: new Date(),
                    createdBy: 'system',
                    lastUpdated: new Date()
                },
                {
                    water_course_rd: '2400/R',
                    channel_name: 'Narwana minor',
                    physical_progress: 45,
                    aa_no_date: 'AA-002/2024 dt. 20.01.2024',
                    type_of_work: 'Water Course Construction',
                    total_length_to_be_lined: '1800 RFT',
                    length_lined: '810 RFT',
                    contractor: 'M/s XYZ Builders',
                    junior_engineer: 'Er. Rajesh Sharma',
                    remarks: 'Delayed due to weather conditions',
                    village: 'Narwana',
                    constituency: 'Narwana',
                    district: 'Kaithal',
                    subdivision: 'narwana',
                    createdAt: new Date(),
                    createdBy: 'system',
                    lastUpdated: new Date()
                },
                {
                    water_course_rd: '124500/L',
                    channel_name: 'Barwala distributary',
                    physical_progress: 90,
                    aa_no_date: 'AA-003/2024 dt. 10.01.2024',
                    type_of_work: 'Canal Maintenance',
                    total_length_to_be_lined: '1200 RFT',
                    length_lined: '1080 RFT',
                    contractor: 'M/s PQR Infrastructure',
                    junior_engineer: 'Er. Amit Singh',
                    remarks: 'Near completion, quality checks ongoing',
                    village: 'Barwala',
                    constituency: 'Hisar',
                    district: 'Hisar',
                    subdivision: 'barwala',
                    createdAt: new Date(),
                    createdBy: 'system',
                    lastUpdated: new Date()
                }
            ];

            for (const work of newWorkStatus) {
                await addDoc(collection(db, 'work_status'), work);
                log(`  ✓ Created work: ${work.water_course_rd} - ${work.channel_name} (${work.subdivision})`);
            }

            log(`✅ Created ${newWorkStatus.length} new work status records`);
        }

        log('🔥 Database reset script loaded successfully');
        log('⚠️ WARNING: This will delete ALL existing data!');
        log('Check the confirmation box and click "Reset Database" to proceed');
    </script>
</body>
</html>
